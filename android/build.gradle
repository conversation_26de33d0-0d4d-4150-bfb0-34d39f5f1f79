group 'io.flutter.plugins.webviewflutter'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '2.1.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.9.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'io.flutter.plugins.webviewflutter'
    compileSdk = flutter.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lintOptions {
        checkAllWarnings true
        warningsAsErrors true
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }

    dependencies {
        implementation 'androidx.annotation:annotation:1.9.1'
        implementation 'androidx.webkit:webkit:1.12.1'
        testImplementation 'junit:junit:4.13.2'
        testImplementation 'org.mockito:mockito-core:5.16.1'
        testImplementation 'org.mockito:mockito-inline:5.1.0'
        testImplementation 'androidx.test:core:1.4.0'
        // https://mvnrepository.com/artifact/com.tencent.tbs/tbssdk
        implementation 'com.tencent.tbs:tbssdk:44286'
    }

    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
