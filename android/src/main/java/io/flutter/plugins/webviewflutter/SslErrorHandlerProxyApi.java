// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.webviewflutter;

import androidx.annotation.NonNull;

import com.tencent.smtt.export.external.interfaces.SslErrorHandler;

/**
 * ProxyApi implementation for {@link SslErrorHandler}. This class may handle instantiating native
 * object instances that are attached to a Dart instance or handle method calls on the associated
 * native class or an instance of that class.
 */
class SslErrorHandlerProxyApi extends PigeonApiSslErrorHandler {
  SslErrorHandlerProxyApi(@NonNull ProxyApiRegistrar pigeonRegistrar) {
    super(pigeonRegistrar);
  }

  @Override
  public void cancel(@NonNull SslErrorHandler pigeon_instance) {
    pigeon_instance.cancel();
  }

  @Override
  public void proceed(@NonNull SslErrorHandler pigeon_instance) {
    pigeon_instance.proceed();
  }
}
