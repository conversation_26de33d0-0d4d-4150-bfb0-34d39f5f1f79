import 'package:flutter/material.dart';
import 'package:webview_flutter_android_example/legacy/web_view.dart';
import 'package:webview_flutter_android_example/main.dart';

class InitWidget extends StatefulWidget {
  const InitWidget({super.key});

  @override
  State<InitWidget> createState() => _InitWidgetState();
}

class _InitWidgetState extends State<InitWidget> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Init Widget'),
      ),
      body: Center(
        child: TextButton(
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) => WebViewExample()));
            },
            child: Text('This is the Init Widget screen.')),
      ),
    );
  }
}
