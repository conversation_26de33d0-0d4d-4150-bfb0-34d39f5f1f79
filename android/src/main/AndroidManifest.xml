<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="io.flutter.plugins.webviewflutter">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application
        android:usesCleartextTraffic="true"
        tools:targetApi="23">
        <!--        <provider-->
        <!--            android:name="androidx.core.content.FileProvider"-->
        <!--            android:authorities="${applicationId}.fileprovider"-->
        <!--            android:exported="false"-->
        <!--            android:grantUriPermissions="true"-->
        <!--            tools:replace="android:authorities">-->
        <!--            <meta-data-->
        <!--                android:name="android.support.FILE_PROVIDER_PATHS"-->
        <!--                android:resource="@xml/filepaths" />-->
        <!--        </provider>-->

        <!--        <provider-->
        <!--            android:name="io.flutter.plugins.webviewflutter.MyProvider"-->
        <!--            android:authorities="${applicationId}.MyProvider"-->
        <!--            android:exported="false" />-->

        <service
            android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService"
            android:label="dexopt"
            android:process=":dexopt" />
    </application>

    <uses-permission
        android:name="android.permission.CAMERA"
        tools:ignore="ManifestOrder" />
</manifest>
