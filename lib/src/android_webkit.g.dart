// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from <PERSON><PERSON> (v25.3.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, prefer_null_aware_operators, omit_local_variable_types, unused_shown_name, unnecessary_import, no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:typed_data' show Float<PERSON><PERSON>ist, Int32List, Int64List, Uint8List;

import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer, immutable, protected;
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart' show WidgetsFlutterBinding;

PlatformException _createConnectionError(String channelName) {
  return PlatformException(
    code: 'channel-error',
    message: 'Unable to establish connection on channel: "$channelName".',
  );
}

List<Object?> wrapResponse({Object? result, PlatformException? error, bool empty = false}) {
  if (empty) {
    return <Object?>[];
  }
  if (error == null) {
    return <Object?>[result];
  }
  return <Object?>[error.code, error.message, error.details];
}
/// An immutable object that serves as the base class for all ProxyApis and
/// can provide functional copies of itself.
///
/// All implementers are expected to be [immutable] as defined by the annotation
/// and override [pigeon_copy] returning an instance of itself.
@immutable
abstract class PigeonInternalProxyApiBaseClass {
  /// Construct a [PigeonInternalProxyApiBaseClass].
  PigeonInternalProxyApiBaseClass({
    this.pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
  }) : pigeon_instanceManager =
            pigeon_instanceManager ?? PigeonInstanceManager.instance;

  /// Sends and receives binary data across the Flutter platform barrier.
  ///
  /// If it is null, the default BinaryMessenger will be used, which routes to
  /// the host platform.
  @protected
  final BinaryMessenger? pigeon_binaryMessenger;

  /// Maintains instances stored to communicate with native language objects.
  final PigeonInstanceManager pigeon_instanceManager;

  /// Instantiates and returns a functionally identical object to oneself.
  ///
  /// Outside of tests, this method should only ever be called by
  /// [PigeonInstanceManager].
  ///
  /// Subclasses should always override their parent's implementation of this
  /// method.
  @protected
  PigeonInternalProxyApiBaseClass pigeon_copy();
}

/// Maintains instances used to communicate with the native objects they
/// represent.
///
/// Added instances are stored as weak references and their copies are stored
/// as strong references to maintain access to their variables and callback
/// methods. Both are stored with the same identifier.
///
/// When a weak referenced instance becomes inaccessible,
/// [onWeakReferenceRemoved] is called with its associated identifier.
///
/// If an instance is retrieved and has the possibility to be used,
/// (e.g. calling [getInstanceWithWeakReference]) a copy of the strong reference
/// is added as a weak reference with the same identifier. This prevents a
/// scenario where the weak referenced instance was released and then later
/// returned by the host platform.
class PigeonInstanceManager {
  /// Constructs a [PigeonInstanceManager].
  PigeonInstanceManager({required void Function(int) onWeakReferenceRemoved}) {
    this.onWeakReferenceRemoved = (int identifier) {
      _weakInstances.remove(identifier);
      onWeakReferenceRemoved(identifier);
    };
    _finalizer = Finalizer<int>(this.onWeakReferenceRemoved);
  }

  // Identifiers are locked to a specific range to avoid collisions with objects
  // created simultaneously by the host platform.
  // Host uses identifiers >= 2^16 and Dart is expected to use values n where,
  // 0 <= n < 2^16.
  static const int _maxDartCreatedIdentifier = 65536;

  /// The default [PigeonInstanceManager] used by ProxyApis.
  ///
  /// On creation, this manager makes a call to clear the native
  /// InstanceManager. This is to prevent identifier conflicts after a host
  /// restart.
  static final PigeonInstanceManager instance = _initInstance();

  // Expando is used because it doesn't prevent its keys from becoming
  // inaccessible. This allows the manager to efficiently retrieve an identifier
  // of an instance without holding a strong reference to that instance.
  //
  // It also doesn't use `==` to search for identifiers, which would lead to an
  // infinite loop when comparing an object to its copy. (i.e. which was caused
  // by calling instanceManager.getIdentifier() inside of `==` while this was a
  // HashMap).
  final Expando<int> _identifiers = Expando<int>();
  final Map<int, WeakReference<PigeonInternalProxyApiBaseClass>> _weakInstances =
      <int, WeakReference<PigeonInternalProxyApiBaseClass>>{};
  final Map<int, PigeonInternalProxyApiBaseClass> _strongInstances = <int, PigeonInternalProxyApiBaseClass>{};
  late final Finalizer<int> _finalizer;
  int _nextIdentifier = 0;

  /// Called when a weak referenced instance is removed by [removeWeakReference]
  /// or becomes inaccessible.
  late final void Function(int) onWeakReferenceRemoved;

  static PigeonInstanceManager _initInstance() {
    WidgetsFlutterBinding.ensureInitialized();
    final _PigeonInternalInstanceManagerApi api = _PigeonInternalInstanceManagerApi();
    // Clears the native `PigeonInstanceManager` on the initial use of the Dart one.
    api.clear();
    final PigeonInstanceManager instanceManager = PigeonInstanceManager(
      onWeakReferenceRemoved: (int identifier) {
        api.removeStrongReference(identifier);
      },
    );
    _PigeonInternalInstanceManagerApi.setUpMessageHandlers(instanceManager: instanceManager);
    WebResourceRequest.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebResourceResponse.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebResourceError.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebResourceErrorCompat.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebViewPoint.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    ConsoleMessage.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    CookieManager.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebView.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebSettings.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    JavaScriptChannel.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebViewClient.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    DownloadListener.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebChromeClient.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    FlutterAssetManager.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    WebStorage.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    FileChooserParams.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    PermissionRequest.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    CustomViewCallback.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    View.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    GeolocationPermissionsCallback.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    HttpAuthHandler.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    AndroidMessage.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    ClientCertRequest.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    PrivateKey.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    X509Certificate.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    SslErrorHandler.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    SslError.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    SslCertificateDName.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    SslCertificate.pigeon_setUpMessageHandlers(pigeon_instanceManager: instanceManager);
    return instanceManager;
  }

  /// Adds a new instance that was instantiated by Dart.
  ///
  /// In other words, Dart wants to add a new instance that will represent
  /// an object that will be instantiated on the host platform.
  ///
  /// Throws assertion error if the instance has already been added.
  ///
  /// Returns the randomly generated id of the [instance] added.
  int addDartCreatedInstance(PigeonInternalProxyApiBaseClass instance) {
    final int identifier = _nextUniqueIdentifier();
    _addInstanceWithIdentifier(instance, identifier);
    return identifier;
  }

  /// Removes the instance, if present, and call [onWeakReferenceRemoved] with
  /// its identifier.
  ///
  /// Returns the identifier associated with the removed instance. Otherwise,
  /// `null` if the instance was not found in this manager.
  ///
  /// This does not remove the strong referenced instance associated with
  /// [instance]. This can be done with [remove].
  int? removeWeakReference(PigeonInternalProxyApiBaseClass instance) {
    final int? identifier = getIdentifier(instance);
    if (identifier == null) {
      return null;
    }

    _identifiers[instance] = null;
    _finalizer.detach(instance);
    onWeakReferenceRemoved(identifier);

    return identifier;
  }

  /// Removes [identifier] and its associated strongly referenced instance, if
  /// present, from the manager.
  ///
  /// Returns the strong referenced instance associated with [identifier] before
  /// it was removed. Returns `null` if [identifier] was not associated with
  /// any strong reference.
  ///
  /// This does not remove the weak referenced instance associated with
  /// [identifier]. This can be done with [removeWeakReference].
  T? remove<T extends PigeonInternalProxyApiBaseClass>(int identifier) {
    return _strongInstances.remove(identifier) as T?;
  }

  /// Retrieves the instance associated with identifier.
  ///
  /// The value returned is chosen from the following order:
  ///
  /// 1. A weakly referenced instance associated with identifier.
  /// 2. If the only instance associated with identifier is a strongly
  /// referenced instance, a copy of the instance is added as a weak reference
  /// with the same identifier. Returning the newly created copy.
  /// 3. If no instance is associated with identifier, returns null.
  ///
  /// This method also expects the host `InstanceManager` to have a strong
  /// reference to the instance the identifier is associated with.
  T? getInstanceWithWeakReference<T extends PigeonInternalProxyApiBaseClass>(int identifier) {
    final PigeonInternalProxyApiBaseClass? weakInstance = _weakInstances[identifier]?.target;

    if (weakInstance == null) {
      final PigeonInternalProxyApiBaseClass? strongInstance = _strongInstances[identifier];
      if (strongInstance != null) {
        final PigeonInternalProxyApiBaseClass copy = strongInstance.pigeon_copy();
        _identifiers[copy] = identifier;
        _weakInstances[identifier] = WeakReference<PigeonInternalProxyApiBaseClass>(copy);
        _finalizer.attach(copy, identifier, detach: copy);
        return copy as T;
      }
      return strongInstance as T?;
    }

    return weakInstance as T;
  }

  /// Retrieves the identifier associated with instance.
  int? getIdentifier(PigeonInternalProxyApiBaseClass instance) {
    return _identifiers[instance];
  }

  /// Adds a new instance that was instantiated by the host platform.
  ///
  /// In other words, the host platform wants to add a new instance that
  /// represents an object on the host platform. Stored with [identifier].
  ///
  /// Throws assertion error if the instance or its identifier has already been
  /// added.
  ///
  /// Returns unique identifier of the [instance] added.
  void addHostCreatedInstance(PigeonInternalProxyApiBaseClass instance, int identifier) {
    _addInstanceWithIdentifier(instance, identifier);
  }

  void _addInstanceWithIdentifier(PigeonInternalProxyApiBaseClass instance, int identifier) {
    assert(!containsIdentifier(identifier));
    assert(getIdentifier(instance) == null);
    assert(identifier >= 0);

    _identifiers[instance] = identifier;
    _weakInstances[identifier] = WeakReference<PigeonInternalProxyApiBaseClass>(instance);
    _finalizer.attach(instance, identifier, detach: instance);

    final PigeonInternalProxyApiBaseClass copy = instance.pigeon_copy();
    _identifiers[copy] = identifier;
    _strongInstances[identifier] = copy;
  }

  /// Whether this manager contains the given [identifier].
  bool containsIdentifier(int identifier) {
    return _weakInstances.containsKey(identifier) ||
        _strongInstances.containsKey(identifier);
  }

  int _nextUniqueIdentifier() {
    late int identifier;
    do {
      identifier = _nextIdentifier;
      _nextIdentifier = (_nextIdentifier + 1) % _maxDartCreatedIdentifier;
    } while (containsIdentifier(identifier));
    return identifier;
  }
}

/// Generated API for managing the Dart and native `PigeonInstanceManager`s.
class _PigeonInternalInstanceManagerApi {
  /// Constructor for [_PigeonInternalInstanceManagerApi].
  _PigeonInternalInstanceManagerApi({BinaryMessenger? binaryMessenger})
      : pigeonVar_binaryMessenger = binaryMessenger;

  final BinaryMessenger? pigeonVar_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  static void setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? binaryMessenger,
    PigeonInstanceManager? instanceManager,
  }) {
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.removeStrongReference',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.removeStrongReference was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_identifier = (args[0] as int?);
          assert(arg_identifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.removeStrongReference was null, expected non-null int.');
          try {
            (instanceManager ?? PigeonInstanceManager.instance)
                .remove(arg_identifier!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  Future<void> removeStrongReference(int identifier) async {
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.removeStrongReference';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[identifier]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Clear the native `PigeonInstanceManager`.
  ///
  /// This is typically called after a hot restart.
  Future<void> clear() async {
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.clear';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture = pigeonVar_channel.send(null);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }
}

class _PigeonInternalProxyApiBaseCodec extends _PigeonCodec {
 const _PigeonInternalProxyApiBaseCodec(this.instanceManager);
 final PigeonInstanceManager instanceManager;
 @override
 void writeValue(WriteBuffer buffer, Object? value) {
   if (value is PigeonInternalProxyApiBaseClass) {
     buffer.putUint8(128);
     writeValue(buffer, instanceManager.getIdentifier(value));
   } else {
     super.writeValue(buffer, value);
   }
 }
 @override
 Object? readValueOfType(int type, ReadBuffer buffer) {
   switch (type) {
     case 128:
       return instanceManager
           .getInstanceWithWeakReference(readValue(buffer)! as int);
     default:
       return super.readValueOfType(type, buffer);
   }
 }
}


/// Mode of how to select files for a file chooser.
///
/// See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams.
enum FileChooserMode {
  /// Open single file and requires that the file exists before allowing the
  /// user to pick it.
  ///
  /// See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_OPEN.
  open,
  /// Similar to [open] but allows multiple files to be selected.
  ///
  /// See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_OPEN_MULTIPLE.
  openMultiple,
  /// Allows picking a nonexistent file and saving it.
  ///
  /// See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_SAVE.
  save,
  /// Indicates a `FileChooserMode` with an unknown mode.
  ///
  /// This does not represent an actual value provided by the platform and only
  /// indicates a value was provided that isn't currently supported.
  unknown,
}

/// Indicates the type of message logged to the console.
///
/// See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel.
enum ConsoleMessageLevel {
  /// Indicates a message is logged for debugging.
  ///
  /// See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#DEBUG.
  debug,
  /// Indicates a message is provided as an error.
  ///
  /// See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#ERROR.
  error,
  /// Indicates a message is provided as a basic log message.
  ///
  /// See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#LOG.
  log,
  /// Indicates a message is provided as a tip.
  ///
  /// See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#TIP.
  tip,
  /// Indicates a message is provided as a warning.
  ///
  /// See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#WARNING.
  warning,
  /// Indicates a message with an unknown level.
  ///
  /// This does not represent an actual value provided by the platform and only
  /// indicates a value was provided that isn't currently supported.
  unknown,
}

/// Type of error for a SslCertificate.
///
/// See https://developer.android.com/reference/android/net/http/SslError#SSL_DATE_INVALID.
enum SslErrorType {
  /// The date of the certificate is invalid.
  dateInvalid,
  /// The certificate has expired.
  expired,
  /// Hostname mismatch.
  idMismatch,
  /// A generic error occurred.
  invalid,
  /// The certificate is not yet valid.
  notYetValid,
  /// The certificate authority is not trusted.
  untrusted,
  /// The type is not recognized by this wrapper.
  unknown,
}


class _PigeonCodec extends StandardMessageCodec {
  const _PigeonCodec();
  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is int) {
      buffer.putUint8(4);
      buffer.putInt64(value);
    }    else if (value is FileChooserMode) {
      buffer.putUint8(129);
      writeValue(buffer, value.index);
    }    else if (value is ConsoleMessageLevel) {
      buffer.putUint8(130);
      writeValue(buffer, value.index);
    }    else if (value is SslErrorType) {
      buffer.putUint8(131);
      writeValue(buffer, value.index);
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 129: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : FileChooserMode.values[value];
      case 130: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : ConsoleMessageLevel.values[value];
      case 131: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : SslErrorType.values[value];
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}
/// Encompasses parameters to the `WebViewClient.shouldInterceptRequest` method.
///
/// See https://developer.android.com/reference/android/webkit/WebResourceRequest.
class WebResourceRequest extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebResourceRequest] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebResourceRequest.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.url,
    required this.isForMainFrame,
    this.isRedirect,
    required this.hasGesture,
    required this.method,
    this.requestHeaders,
  });

  /// The URL for which the resource request was made.
  final String url;

  /// Whether the request was made in order to fetch the main frame's document.
  final bool isForMainFrame;

  /// Whether the request was a result of a server-side redirect.
  final bool? isRedirect;

  /// Whether a gesture (such as a click) was associated with the request.
  final bool hasGesture;

  /// The method associated with the request, for example "GET".
  final String method;

  /// The headers associated with the request.
  final Map<String, String>? requestHeaders;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebResourceRequest Function(
      String url,
      bool isForMainFrame,
      bool? isRedirect,
      bool hasGesture,
      String method,
      Map<String, String>? requestHeaders,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance was null, expected non-null int.');
          final String? arg_url = (args[1] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance was null, expected non-null String.');
          final bool? arg_isForMainFrame = (args[2] as bool?);
          assert(arg_isForMainFrame != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance was null, expected non-null bool.');
          final bool? arg_isRedirect = (args[3] as bool?);
          final bool? arg_hasGesture = (args[4] as bool?);
          assert(arg_hasGesture != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance was null, expected non-null bool.');
          final String? arg_method = (args[5] as String?);
          assert(arg_method != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance was null, expected non-null String.');
          final Map<String, String>? arg_requestHeaders =
              (args[6] as Map<Object?, Object?>?)?.cast<String, String>();
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(
                      arg_url!,
                      arg_isForMainFrame!,
                      arg_isRedirect,
                      arg_hasGesture!,
                      arg_method!,
                      arg_requestHeaders) ??
                  WebResourceRequest.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    url: arg_url!,
                    isForMainFrame: arg_isForMainFrame!,
                    isRedirect: arg_isRedirect,
                    hasGesture: arg_hasGesture!,
                    method: arg_method!,
                    requestHeaders: arg_requestHeaders,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  WebResourceRequest pigeon_copy() {
    return WebResourceRequest.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      url: url,
      isForMainFrame: isForMainFrame,
      isRedirect: isRedirect,
      hasGesture: hasGesture,
      method: method,
      requestHeaders: requestHeaders,
    );
  }
}

/// Encapsulates a resource response.
///
/// See https://developer.android.com/reference/android/webkit/WebResourceResponse.
class WebResourceResponse extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebResourceResponse] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebResourceResponse.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.statusCode,
  });

  /// The resource response's status code.
  final int statusCode;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebResourceResponse Function(int statusCode)? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebResourceResponse.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceResponse.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceResponse.pigeon_newInstance was null, expected non-null int.');
          final int? arg_statusCode = (args[1] as int?);
          assert(arg_statusCode != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceResponse.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_statusCode!) ??
                  WebResourceResponse.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    statusCode: arg_statusCode!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  WebResourceResponse pigeon_copy() {
    return WebResourceResponse.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      statusCode: statusCode,
    );
  }
}

/// Encapsulates information about errors that occurred during loading of web
/// resources.
///
/// See https://developer.android.com/reference/android/webkit/WebResourceError.
class WebResourceError extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebResourceError] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebResourceError.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.errorCode,
    required this.description,
  });

  /// The error code of the error.
  final int errorCode;

  /// The string describing the error.
  final String description;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebResourceError Function(
      int errorCode,
      String description,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebResourceError.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceError.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceError.pigeon_newInstance was null, expected non-null int.');
          final int? arg_errorCode = (args[1] as int?);
          assert(arg_errorCode != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceError.pigeon_newInstance was null, expected non-null int.');
          final String? arg_description = (args[2] as String?);
          assert(arg_description != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceError.pigeon_newInstance was null, expected non-null String.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_errorCode!, arg_description!) ??
                  WebResourceError.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    errorCode: arg_errorCode!,
                    description: arg_description!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  WebResourceError pigeon_copy() {
    return WebResourceError.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      errorCode: errorCode,
      description: description,
    );
  }
}

/// Encapsulates information about errors that occurred during loading of web
/// resources.
///
/// See https://developer.android.com/reference/androidx/webkit/WebResourceErrorCompat.
class WebResourceErrorCompat extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebResourceErrorCompat] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebResourceErrorCompat.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.errorCode,
    required this.description,
  });

  /// The error code of the error.
  final int errorCode;

  /// The string describing the error.
  final String description;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebResourceErrorCompat Function(
      int errorCode,
      String description,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebResourceErrorCompat.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceErrorCompat.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceErrorCompat.pigeon_newInstance was null, expected non-null int.');
          final int? arg_errorCode = (args[1] as int?);
          assert(arg_errorCode != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceErrorCompat.pigeon_newInstance was null, expected non-null int.');
          final String? arg_description = (args[2] as String?);
          assert(arg_description != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebResourceErrorCompat.pigeon_newInstance was null, expected non-null String.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_errorCode!, arg_description!) ??
                  WebResourceErrorCompat.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    errorCode: arg_errorCode!,
                    description: arg_description!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  WebResourceErrorCompat pigeon_copy() {
    return WebResourceErrorCompat.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      errorCode: errorCode,
      description: description,
    );
  }
}

/// Represents a position on a web page.
///
/// This is a custom class created for convenience of the wrapper.
class WebViewPoint extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebViewPoint] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebViewPoint.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.x,
    required this.y,
  });

  final int x;

  final int y;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebViewPoint Function(
      int x,
      int y,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewPoint.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewPoint.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewPoint.pigeon_newInstance was null, expected non-null int.');
          final int? arg_x = (args[1] as int?);
          assert(arg_x != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewPoint.pigeon_newInstance was null, expected non-null int.');
          final int? arg_y = (args[2] as int?);
          assert(arg_y != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewPoint.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_x!, arg_y!) ??
                  WebViewPoint.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    x: arg_x!,
                    y: arg_y!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  WebViewPoint pigeon_copy() {
    return WebViewPoint.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      x: x,
      y: y,
    );
  }
}

/// Represents a JavaScript console message from WebCore.
///
/// See https://developer.android.com/reference/android/webkit/ConsoleMessage
class ConsoleMessage extends PigeonInternalProxyApiBaseClass {
  /// Constructs [ConsoleMessage] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  ConsoleMessage.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.lineNumber,
    required this.message,
    required this.level,
    required this.sourceId,
  });

  final int lineNumber;

  final String message;

  final ConsoleMessageLevel level;

  final String sourceId;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    ConsoleMessage Function(
      int lineNumber,
      String message,
      ConsoleMessageLevel level,
      String sourceId,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance was null, expected non-null int.');
          final int? arg_lineNumber = (args[1] as int?);
          assert(arg_lineNumber != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance was null, expected non-null int.');
          final String? arg_message = (args[2] as String?);
          assert(arg_message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance was null, expected non-null String.');
          final ConsoleMessageLevel? arg_level =
              (args[3] as ConsoleMessageLevel?);
          assert(arg_level != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance was null, expected non-null ConsoleMessageLevel.');
          final String? arg_sourceId = (args[4] as String?);
          assert(arg_sourceId != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance was null, expected non-null String.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_lineNumber!, arg_message!,
                      arg_level!, arg_sourceId!) ??
                  ConsoleMessage.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    lineNumber: arg_lineNumber!,
                    message: arg_message!,
                    level: arg_level!,
                    sourceId: arg_sourceId!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  ConsoleMessage pigeon_copy() {
    return ConsoleMessage.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      lineNumber: lineNumber,
      message: message,
      level: level,
      sourceId: sourceId,
    );
  }
}

/// Manages the cookies used by an application's `WebView` instances.
///
/// See https://developer.android.com/reference/android/webkit/CookieManager.
class CookieManager extends PigeonInternalProxyApiBaseClass {
  /// Constructs [CookieManager] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  CookieManager.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecCookieManager =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static final CookieManager instance = pigeonVar_instance();

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    CookieManager Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.CookieManager.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.CookieManager.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.CookieManager.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  CookieManager.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  static CookieManager pigeonVar_instance() {
    final CookieManager pigeonVar_instance = CookieManager.pigeon_detached();
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(PigeonInstanceManager.instance);
    final BinaryMessenger pigeonVar_binaryMessenger =
        ServicesBinding.instance.defaultBinaryMessenger;
    final int pigeonVar_instanceIdentifier = PigeonInstanceManager.instance
        .addDartCreatedInstance(pigeonVar_instance);
    () async {
      const String pigeonVar_channelName =
          'dev.flutter.pigeon.webview_flutter_android.CookieManager.instance';
      final BasicMessageChannel<Object?> pigeonVar_channel =
          BasicMessageChannel<Object?>(
        pigeonVar_channelName,
        pigeonChannelCodec,
        binaryMessenger: pigeonVar_binaryMessenger,
      );
      final Future<Object?> pigeonVar_sendFuture =
          pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
    return pigeonVar_instance;
  }

  /// Sets a single cookie (key-value pair) for the given URL.
  Future<void> setCookie(
    String url,
    String value,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecCookieManager;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.CookieManager.setCookie';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, url, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Removes all cookies.
  Future<bool> removeAllCookies() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecCookieManager;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.CookieManager.removeAllCookies';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  /// Sets whether the `WebView` should allow third party cookies to be set.
  Future<void> setAcceptThirdPartyCookies(
    WebView webView,
    bool accept,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecCookieManager;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.CookieManager.setAcceptThirdPartyCookies';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, webView, accept]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  CookieManager pigeon_copy() {
    return CookieManager.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// A View that displays web pages.
///
/// See https://developer.android.com/reference/android/webkit/WebView.
class WebView extends View {
  WebView({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    this.onScrollChanged,
  }) : super.pigeon_detached() {
    final int pigeonVar_instanceIdentifier =
        pigeon_instanceManager.addDartCreatedInstance(this);
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.pigeon_defaultConstructor';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
    () async {
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
  }

  /// Constructs [WebView] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebView.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    this.onScrollChanged,
  }) : super.pigeon_detached();

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecWebView =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  /// This is called in response to an internal scroll in this view (i.e., the
  /// view scrolled its own contents).
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebView instance = WebView(
  ///  onScrollChanged: (WebView pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebView pigeon_instance,
    int left,
    int top,
    int oldLeft,
    int oldTop,
  )? onScrollChanged;

  /// The WebSettings object used to control the settings for this WebView.
  late final WebSettings settings = pigeonVar_settings();

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebView Function()? pigeon_newInstance,
    void Function(
      WebView pigeon_instance,
      int left,
      int top,
      int oldLeft,
      int oldTop,
    )? onScrollChanged,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebView.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  WebView.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebView? arg_pigeon_instance = (args[0] as WebView?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged was null, expected non-null WebView.');
          final int? arg_left = (args[1] as int?);
          assert(arg_left != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged was null, expected non-null int.');
          final int? arg_top = (args[2] as int?);
          assert(arg_top != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged was null, expected non-null int.');
          final int? arg_oldLeft = (args[3] as int?);
          assert(arg_oldLeft != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged was null, expected non-null int.');
          final int? arg_oldTop = (args[4] as int?);
          assert(arg_oldTop != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged was null, expected non-null int.');
          try {
            (onScrollChanged ?? arg_pigeon_instance!.onScrollChanged)?.call(
                arg_pigeon_instance!,
                arg_left!,
                arg_top!,
                arg_oldLeft!,
                arg_oldTop!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  WebSettings pigeonVar_settings() {
    final WebSettings pigeonVar_instance = WebSettings.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    final int pigeonVar_instanceIdentifier =
        pigeon_instanceManager.addDartCreatedInstance(pigeonVar_instance);
    () async {
      const String pigeonVar_channelName =
          'dev.flutter.pigeon.webview_flutter_android.WebView.settings';
      final BasicMessageChannel<Object?> pigeonVar_channel =
          BasicMessageChannel<Object?>(
        pigeonVar_channelName,
        pigeonChannelCodec,
        binaryMessenger: pigeonVar_binaryMessenger,
      );
      final Future<Object?> pigeonVar_sendFuture =
          pigeonVar_channel.send(<Object?>[this, pigeonVar_instanceIdentifier]);
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
    return pigeonVar_instance;
  }

  /// Loads the given data into this WebView using a 'data' scheme URL.
  Future<void> loadData(
    String data,
    String? mimeType,
    String? encoding,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.loadData';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, data, mimeType, encoding]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Loads the given data into this WebView, using baseUrl as the base URL for
  /// the content.
  Future<void> loadDataWithBaseUrl(
    String? baseUrl,
    String data,
    String? mimeType,
    String? encoding,
    String? historyUrl,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.loadDataWithBaseUrl';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture = pigeonVar_channel
        .send(<Object?>[this, baseUrl, data, mimeType, encoding, historyUrl]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Loads the given URL.
  Future<void> loadUrl(
    String url,
    Map<String, String> headers,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.loadUrl';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, url, headers]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Loads the URL with postData using "POST" method into this WebView.
  Future<void> postUrl(
    String url,
    Uint8List data,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.postUrl';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, url, data]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Gets the URL for the current page.
  Future<String?> getUrl() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.getUrl';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as String?);
    }
  }

  /// Gets whether this WebView has a back history item.
  Future<bool> canGoBack() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.canGoBack';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  /// Gets whether this WebView has a forward history item.
  Future<bool> canGoForward() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.canGoForward';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  /// Goes back in the history of this WebView.
  Future<void> goBack() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.goBack';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Goes forward in the history of this WebView.
  Future<void> goForward() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.goForward';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Reloads the current URL.
  Future<void> reload() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.reload';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Clears the resource cache.
  Future<void> clearCache(bool includeDiskFiles) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.clearCache';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, includeDiskFiles]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Asynchronously evaluates JavaScript in the context of the currently
  /// displayed page.
  Future<String?> evaluateJavascript(String javascriptString) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.evaluateJavascript';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, javascriptString]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as String?);
    }
  }

  /// Gets the title for the current page.
  Future<String?> getTitle() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.getTitle';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as String?);
    }
  }

  /// Enables debugging of web contents (HTML / CSS / JavaScript) loaded into
  /// any WebViews of this application.
  static Future<void> setWebContentsDebuggingEnabled(
    bool enabled, {
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
  }) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.setWebContentsDebuggingEnabled';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[enabled]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the WebViewClient that will receive various notifications and
  /// requests.
  Future<void> setWebViewClient(WebViewClient? client) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.setWebViewClient';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, client]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Injects the supplied Java object into this WebView.
  Future<void> addJavaScriptChannel(JavaScriptChannel channel) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.addJavaScriptChannel';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, channel]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Removes a previously injected Java object from this WebView.
  Future<void> removeJavaScriptChannel(String name) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.removeJavaScriptChannel';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, name]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Registers the interface to be used when content can not be handled by the
  /// rendering engine, and should be downloaded instead.
  Future<void> setDownloadListener(DownloadListener? listener) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.setDownloadListener';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, listener]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the chrome handler.
  Future<void> setWebChromeClient(WebChromeClient? client) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.setWebChromeClient';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, client]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the background color for this view.
  Future<void> setBackgroundColor(int color) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.setBackgroundColor';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, color]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Destroys the internal state of this WebView.
  Future<void> destroy() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebView.destroy';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  WebView pigeon_copy() {
    return WebView.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      onScrollChanged: onScrollChanged,
    );
  }
}

/// Manages settings state for a `WebView`.
///
/// See https://developer.android.com/reference/android/webkit/WebSettings.
class WebSettings extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebSettings] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebSettings.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecWebSettings =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebSettings Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebSettings.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebSettings.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebSettings.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  WebSettings.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Sets whether the DOM storage API is enabled.
  Future<void> setDomStorageEnabled(bool flag) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setDomStorageEnabled';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, flag]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Tells JavaScript to open windows automatically.
  Future<void> setJavaScriptCanOpenWindowsAutomatically(bool flag) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setJavaScriptCanOpenWindowsAutomatically';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, flag]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView whether supports multiple windows.
  Future<void> setSupportMultipleWindows(bool support) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setSupportMultipleWindows';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, support]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Tells the WebView to enable JavaScript execution.
  Future<void> setJavaScriptEnabled(bool flag) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setJavaScriptEnabled';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, flag]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the WebView's user-agent string.
  Future<void> setUserAgentString(String? userAgentString) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setUserAgentString';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, userAgentString]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView requires a user gesture to play media.
  Future<void> setMediaPlaybackRequiresUserGesture(bool require) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setMediaPlaybackRequiresUserGesture';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, require]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView should support zooming using its on-screen zoom
  /// controls and gestures.
  Future<void> setSupportZoom(bool support) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setSupportZoom';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, support]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView loads pages in overview mode, that is, zooms out
  /// the content to fit on screen by width.
  Future<void> setLoadWithOverviewMode(bool overview) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setLoadWithOverviewMode';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, overview]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView should enable support for the "viewport" HTML
  /// meta tag or should use a wide viewport.
  Future<void> setUseWideViewPort(bool use) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setUseWideViewPort';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, use]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView should display on-screen zoom controls when using
  /// the built-in zoom mechanisms.
  Future<void> setDisplayZoomControls(bool enabled) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setDisplayZoomControls';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, enabled]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether the WebView should display on-screen zoom controls when using
  /// the built-in zoom mechanisms.
  Future<void> setBuiltInZoomControls(bool enabled) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setBuiltInZoomControls';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, enabled]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Enables or disables file access within WebView.
  Future<void> setAllowFileAccess(bool enabled) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setAllowFileAccess';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, enabled]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Enables or disables content URL access within WebView.
  Future<void> setAllowContentAccess(bool enabled) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setAllowContentAccess';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, enabled]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets whether Geolocation is enabled within WebView.
  Future<void> setGeolocationEnabled(bool enabled) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setGeolocationEnabled';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, enabled]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the text zoom of the page in percent.
  Future<void> setTextZoom(int textZoom) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.setTextZoom';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, textZoom]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Gets the WebView's user-agent string.
  Future<String> getUserAgentString() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebSettings;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebSettings.getUserAgentString';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as String?)!;
    }
  }

  @override
  WebSettings pigeon_copy() {
    return WebSettings.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// A JavaScript interface for exposing Javascript callbacks to Dart.
///
/// This is a custom class for the wrapper that is annotated with
/// [JavascriptInterface](https://developer.android.com/reference/android/webkit/JavascriptInterface).
class JavaScriptChannel extends PigeonInternalProxyApiBaseClass {
  JavaScriptChannel({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.channelName,
    required this.postMessage,
  }) {
    final int pigeonVar_instanceIdentifier =
        pigeon_instanceManager.addDartCreatedInstance(this);
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecJavaScriptChannel;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.pigeon_defaultConstructor';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture = pigeonVar_channel
        .send(<Object?>[pigeonVar_instanceIdentifier, channelName]);
    () async {
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
  }

  /// Constructs [JavaScriptChannel] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  JavaScriptChannel.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.channelName,
    required this.postMessage,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecJavaScriptChannel =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  final String channelName;

  /// Handles callbacks messages from JavaScript.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final JavaScriptChannel instance = JavaScriptChannel(
  ///  postMessage: (JavaScriptChannel pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    JavaScriptChannel pigeon_instance,
    String message,
  ) postMessage;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    void Function(
      JavaScriptChannel pigeon_instance,
      String message,
    )? postMessage,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.postMessage',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.postMessage was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final JavaScriptChannel? arg_pigeon_instance =
              (args[0] as JavaScriptChannel?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.postMessage was null, expected non-null JavaScriptChannel.');
          final String? arg_message = (args[1] as String?);
          assert(arg_message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.postMessage was null, expected non-null String.');
          try {
            (postMessage ?? arg_pigeon_instance!.postMessage)
                .call(arg_pigeon_instance!, arg_message!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  JavaScriptChannel pigeon_copy() {
    return JavaScriptChannel.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      channelName: channelName,
      postMessage: postMessage,
    );
  }
}

/// Receives various notifications and requests from a `WebView`.
///
/// See https://developer.android.com/reference/android/webkit/WebViewClient.
class WebViewClient extends PigeonInternalProxyApiBaseClass {
  WebViewClient({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    this.onPageStarted,
    this.onPageFinished,
    this.onReceivedHttpError,
    this.onReceivedRequestError,
    this.onReceivedRequestErrorCompat,
    this.onReceivedError,
    this.requestLoading,
    this.urlLoading,
    this.doUpdateVisitedHistory,
    this.onReceivedHttpAuthRequest,
    this.onFormResubmission,
    this.onLoadResource,
    this.onPageCommitVisible,
    this.onReceivedClientCertRequest,
    this.onReceivedLoginRequest,
    this.onReceivedSslError,
    this.onScaleChanged,
  }) {
    final int pigeonVar_instanceIdentifier =
        pigeon_instanceManager.addDartCreatedInstance(this);
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebViewClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebViewClient.pigeon_defaultConstructor';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
    () async {
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
  }

  /// Constructs [WebViewClient] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebViewClient.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    this.onPageStarted,
    this.onPageFinished,
    this.onReceivedHttpError,
    this.onReceivedRequestError,
    this.onReceivedRequestErrorCompat,
    this.onReceivedError,
    this.requestLoading,
    this.urlLoading,
    this.doUpdateVisitedHistory,
    this.onReceivedHttpAuthRequest,
    this.onFormResubmission,
    this.onLoadResource,
    this.onPageCommitVisible,
    this.onReceivedClientCertRequest,
    this.onReceivedLoginRequest,
    this.onReceivedSslError,
    this.onScaleChanged,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecWebViewClient =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  /// Notify the host application that a page has started loading.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onPageStarted: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    String url,
  )? onPageStarted;

  /// Notify the host application that a page has finished loading.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onPageFinished: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    String url,
  )? onPageFinished;

  /// Notify the host application that an HTTP error has been received from the
  /// server while loading a resource.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedHttpError: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    WebResourceRequest request,
    WebResourceResponse response,
  )? onReceivedHttpError;

  /// Report web resource loading error to the host application.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedRequestError: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    WebResourceRequest request,
    WebResourceError error,
  )? onReceivedRequestError;

  /// Report web resource loading error to the host application.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedRequestErrorCompat: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    WebResourceRequest request,
    WebResourceErrorCompat error,
  )? onReceivedRequestErrorCompat;

  /// Report an error to the host application.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedError: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    int errorCode,
    String description,
    String failingUrl,
  )? onReceivedError;

  /// Give the host application a chance to take control when a URL is about to
  /// be loaded in the current WebView.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  requestLoading: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    WebResourceRequest request,
  )? requestLoading;

  /// Give the host application a chance to take control when a URL is about to
  /// be loaded in the current WebView.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  urlLoading: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    String url,
  )? urlLoading;

  /// Notify the host application to update its visited links database.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  doUpdateVisitedHistory: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    String url,
    bool isReload,
  )? doUpdateVisitedHistory;

  /// Notifies the host application that the WebView received an HTTP
  /// authentication request.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedHttpAuthRequest: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView webView,
    HttpAuthHandler handler,
    String host,
    String realm,
  )? onReceivedHttpAuthRequest;

  /// Ask the host application if the browser should resend data as the
  /// requested page was a result of a POST.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onFormResubmission: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    AndroidMessage dontResend,
    AndroidMessage resend,
  )? onFormResubmission;

  /// Notify the host application that the WebView will load the resource
  /// specified by the given url.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onLoadResource: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    String url,
  )? onLoadResource;

  /// Notify the host application that WebView content left over from previous
  /// page navigations will no longer be drawn.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onPageCommitVisible: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    String url,
  )? onPageCommitVisible;

  /// Notify the host application to handle a SSL client certificate request.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedClientCertRequest: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    ClientCertRequest request,
  )? onReceivedClientCertRequest;

  /// Notify the host application that a request to automatically log in the
  /// user has been processed.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedLoginRequest: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    String realm,
    String? account,
    String args,
  )? onReceivedLoginRequest;

  /// Notifies the host application that an SSL error occurred while loading a
  /// resource.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onReceivedSslError: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    SslErrorHandler handler,
    SslError error,
  )? onReceivedSslError;

  /// Notify the host application that the scale applied to the WebView has
  /// changed.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebViewClient instance = WebViewClient(
  ///  onScaleChanged: (WebViewClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebViewClient pigeon_instance,
    WebView view,
    double oldScale,
    double newScale,
  )? onScaleChanged;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebViewClient Function()? pigeon_newInstance,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      String url,
    )? onPageStarted,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      String url,
    )? onPageFinished,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      WebResourceRequest request,
      WebResourceResponse response,
    )? onReceivedHttpError,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      WebResourceRequest request,
      WebResourceError error,
    )? onReceivedRequestError,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      WebResourceRequest request,
      WebResourceErrorCompat error,
    )? onReceivedRequestErrorCompat,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      int errorCode,
      String description,
      String failingUrl,
    )? onReceivedError,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      WebResourceRequest request,
    )? requestLoading,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      String url,
    )? urlLoading,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      String url,
      bool isReload,
    )? doUpdateVisitedHistory,
    void Function(
      WebViewClient pigeon_instance,
      WebView webView,
      HttpAuthHandler handler,
      String host,
      String realm,
    )? onReceivedHttpAuthRequest,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      AndroidMessage dontResend,
      AndroidMessage resend,
    )? onFormResubmission,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      String url,
    )? onLoadResource,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      String url,
    )? onPageCommitVisible,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      ClientCertRequest request,
    )? onReceivedClientCertRequest,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      String realm,
      String? account,
      String args,
    )? onReceivedLoginRequest,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      SslErrorHandler handler,
      SslError error,
    )? onReceivedSslError,
    void Function(
      WebViewClient pigeon_instance,
      WebView view,
      double oldScale,
      double newScale,
    )? onScaleChanged,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  WebViewClient.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageStarted',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageStarted was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageStarted was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageStarted was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageStarted was null, expected non-null String.');
          try {
            (onPageStarted ?? arg_pigeon_instance!.onPageStarted)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_url!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageFinished',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageFinished was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageFinished was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageFinished was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageFinished was null, expected non-null String.');
          try {
            (onPageFinished ?? arg_pigeon_instance!.onPageFinished)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_url!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError was null, expected non-null WebView.');
          final WebResourceRequest? arg_request =
              (args[2] as WebResourceRequest?);
          assert(arg_request != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError was null, expected non-null WebResourceRequest.');
          final WebResourceResponse? arg_response =
              (args[3] as WebResourceResponse?);
          assert(arg_response != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError was null, expected non-null WebResourceResponse.');
          try {
            (onReceivedHttpError ?? arg_pigeon_instance!.onReceivedHttpError)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_request!,
                    arg_response!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError was null, expected non-null WebView.');
          final WebResourceRequest? arg_request =
              (args[2] as WebResourceRequest?);
          assert(arg_request != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError was null, expected non-null WebResourceRequest.');
          final WebResourceError? arg_error = (args[3] as WebResourceError?);
          assert(arg_error != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError was null, expected non-null WebResourceError.');
          try {
            (onReceivedRequestError ??
                    arg_pigeon_instance!.onReceivedRequestError)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_request!,
                    arg_error!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat was null, expected non-null WebView.');
          final WebResourceRequest? arg_request =
              (args[2] as WebResourceRequest?);
          assert(arg_request != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat was null, expected non-null WebResourceRequest.');
          final WebResourceErrorCompat? arg_error =
              (args[3] as WebResourceErrorCompat?);
          assert(arg_error != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat was null, expected non-null WebResourceErrorCompat.');
          try {
            (onReceivedRequestErrorCompat ??
                    arg_pigeon_instance!.onReceivedRequestErrorCompat)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_request!,
                    arg_error!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError was null, expected non-null WebView.');
          final int? arg_errorCode = (args[2] as int?);
          assert(arg_errorCode != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError was null, expected non-null int.');
          final String? arg_description = (args[3] as String?);
          assert(arg_description != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError was null, expected non-null String.');
          final String? arg_failingUrl = (args[4] as String?);
          assert(arg_failingUrl != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError was null, expected non-null String.');
          try {
            (onReceivedError ?? arg_pigeon_instance!.onReceivedError)?.call(
                arg_pigeon_instance!,
                arg_webView!,
                arg_errorCode!,
                arg_description!,
                arg_failingUrl!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.requestLoading',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.requestLoading was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.requestLoading was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.requestLoading was null, expected non-null WebView.');
          final WebResourceRequest? arg_request =
              (args[2] as WebResourceRequest?);
          assert(arg_request != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.requestLoading was null, expected non-null WebResourceRequest.');
          try {
            (requestLoading ?? arg_pigeon_instance!.requestLoading)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_request!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.urlLoading',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.urlLoading was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.urlLoading was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.urlLoading was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.urlLoading was null, expected non-null String.');
          try {
            (urlLoading ?? arg_pigeon_instance!.urlLoading)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_url!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory was null, expected non-null String.');
          final bool? arg_isReload = (args[3] as bool?);
          assert(arg_isReload != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory was null, expected non-null bool.');
          try {
            (doUpdateVisitedHistory ??
                    arg_pigeon_instance!.doUpdateVisitedHistory)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_url!,
                    arg_isReload!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest was null, expected non-null WebViewClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest was null, expected non-null WebView.');
          final HttpAuthHandler? arg_handler = (args[2] as HttpAuthHandler?);
          assert(arg_handler != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest was null, expected non-null HttpAuthHandler.');
          final String? arg_host = (args[3] as String?);
          assert(arg_host != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest was null, expected non-null String.');
          final String? arg_realm = (args[4] as String?);
          assert(arg_realm != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest was null, expected non-null String.');
          try {
            (onReceivedHttpAuthRequest ??
                    arg_pigeon_instance!.onReceivedHttpAuthRequest)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_handler!,
                    arg_host!, arg_realm!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission was null, expected non-null WebView.');
          final AndroidMessage? arg_dontResend = (args[2] as AndroidMessage?);
          assert(arg_dontResend != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission was null, expected non-null AndroidMessage.');
          final AndroidMessage? arg_resend = (args[3] as AndroidMessage?);
          assert(arg_resend != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission was null, expected non-null AndroidMessage.');
          try {
            (onFormResubmission ?? arg_pigeon_instance!.onFormResubmission)
                ?.call(arg_pigeon_instance!, arg_view!, arg_dontResend!,
                    arg_resend!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onLoadResource',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onLoadResource was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onLoadResource was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onLoadResource was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onLoadResource was null, expected non-null String.');
          try {
            (onLoadResource ?? arg_pigeon_instance!.onLoadResource)
                ?.call(arg_pigeon_instance!, arg_view!, arg_url!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageCommitVisible',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageCommitVisible was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageCommitVisible was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageCommitVisible was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageCommitVisible was null, expected non-null String.');
          try {
            (onPageCommitVisible ?? arg_pigeon_instance!.onPageCommitVisible)
                ?.call(arg_pigeon_instance!, arg_view!, arg_url!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedClientCertRequest',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedClientCertRequest was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedClientCertRequest was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedClientCertRequest was null, expected non-null WebView.');
          final ClientCertRequest? arg_request =
              (args[2] as ClientCertRequest?);
          assert(arg_request != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedClientCertRequest was null, expected non-null ClientCertRequest.');
          try {
            (onReceivedClientCertRequest ??
                    arg_pigeon_instance!.onReceivedClientCertRequest)
                ?.call(arg_pigeon_instance!, arg_view!, arg_request!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest was null, expected non-null WebView.');
          final String? arg_realm = (args[2] as String?);
          assert(arg_realm != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest was null, expected non-null String.');
          final String? arg_account = (args[3] as String?);
          final String? arg_args = (args[4] as String?);
          assert(arg_args != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest was null, expected non-null String.');
          try {
            (onReceivedLoginRequest ??
                    arg_pigeon_instance!.onReceivedLoginRequest)
                ?.call(arg_pigeon_instance!, arg_view!, arg_realm!, arg_account,
                    arg_args!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError was null, expected non-null WebView.');
          final SslErrorHandler? arg_handler = (args[2] as SslErrorHandler?);
          assert(arg_handler != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError was null, expected non-null SslErrorHandler.');
          final SslError? arg_error = (args[3] as SslError?);
          assert(arg_error != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError was null, expected non-null SslError.');
          try {
            (onReceivedSslError ?? arg_pigeon_instance!.onReceivedSslError)
                ?.call(
                    arg_pigeon_instance!, arg_view!, arg_handler!, arg_error!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebViewClient? arg_pigeon_instance =
              (args[0] as WebViewClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged was null, expected non-null WebViewClient.');
          final WebView? arg_view = (args[1] as WebView?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged was null, expected non-null WebView.');
          final double? arg_oldScale = (args[2] as double?);
          assert(arg_oldScale != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged was null, expected non-null double.');
          final double? arg_newScale = (args[3] as double?);
          assert(arg_newScale != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged was null, expected non-null double.');
          try {
            (onScaleChanged ?? arg_pigeon_instance!.onScaleChanged)?.call(
                arg_pigeon_instance!, arg_view!, arg_oldScale!, arg_newScale!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Sets the required synchronous return value for the Java method,
  /// `WebViewClient.shouldOverrideUrlLoading(...)`.
  ///
  /// The Java method, `WebViewClient.shouldOverrideUrlLoading(...)`, requires
  /// a boolean to be returned and this method sets the returned value for all
  /// calls to the Java method.
  ///
  /// Setting this to true causes the current [WebView] to abort loading any URL
  /// received by [requestLoading] or [urlLoading], while setting this to false
  /// causes the [WebView] to continue loading a URL as usual.
  ///
  /// Defaults to false.
  Future<void> setSynchronousReturnValueForShouldOverrideUrlLoading(
      bool value) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebViewClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebViewClient.setSynchronousReturnValueForShouldOverrideUrlLoading';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  WebViewClient pigeon_copy() {
    return WebViewClient.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      onPageStarted: onPageStarted,
      onPageFinished: onPageFinished,
      onReceivedHttpError: onReceivedHttpError,
      onReceivedRequestError: onReceivedRequestError,
      onReceivedRequestErrorCompat: onReceivedRequestErrorCompat,
      onReceivedError: onReceivedError,
      requestLoading: requestLoading,
      urlLoading: urlLoading,
      doUpdateVisitedHistory: doUpdateVisitedHistory,
      onReceivedHttpAuthRequest: onReceivedHttpAuthRequest,
      onFormResubmission: onFormResubmission,
      onLoadResource: onLoadResource,
      onPageCommitVisible: onPageCommitVisible,
      onReceivedClientCertRequest: onReceivedClientCertRequest,
      onReceivedLoginRequest: onReceivedLoginRequest,
      onReceivedSslError: onReceivedSslError,
      onScaleChanged: onScaleChanged,
    );
  }
}

/// Handles notifications that a file should be downloaded.
///
/// See https://developer.android.com/reference/android/webkit/DownloadListener.
class DownloadListener extends PigeonInternalProxyApiBaseClass {
  DownloadListener({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.onDownloadStart,
  }) {
    final int pigeonVar_instanceIdentifier =
        pigeon_instanceManager.addDartCreatedInstance(this);
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecDownloadListener;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.DownloadListener.pigeon_defaultConstructor';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
    () async {
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
  }

  /// Constructs [DownloadListener] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  DownloadListener.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.onDownloadStart,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecDownloadListener =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  /// Notify the host application that a file should be downloaded.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final DownloadListener instance = DownloadListener(
  ///  onDownloadStart: (DownloadListener pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    DownloadListener pigeon_instance,
    String url,
    String userAgent,
    String contentDisposition,
    String mimetype,
    int contentLength,
  ) onDownloadStart;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    void Function(
      DownloadListener pigeon_instance,
      String url,
      String userAgent,
      String contentDisposition,
      String mimetype,
      int contentLength,
    )? onDownloadStart,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final DownloadListener? arg_pigeon_instance =
              (args[0] as DownloadListener?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null, expected non-null DownloadListener.');
          final String? arg_url = (args[1] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null, expected non-null String.');
          final String? arg_userAgent = (args[2] as String?);
          assert(arg_userAgent != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null, expected non-null String.');
          final String? arg_contentDisposition = (args[3] as String?);
          assert(arg_contentDisposition != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null, expected non-null String.');
          final String? arg_mimetype = (args[4] as String?);
          assert(arg_mimetype != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null, expected non-null String.');
          final int? arg_contentLength = (args[5] as int?);
          assert(arg_contentLength != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart was null, expected non-null int.');
          try {
            (onDownloadStart ?? arg_pigeon_instance!.onDownloadStart).call(
                arg_pigeon_instance!,
                arg_url!,
                arg_userAgent!,
                arg_contentDisposition!,
                arg_mimetype!,
                arg_contentLength!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  DownloadListener pigeon_copy() {
    return DownloadListener.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      onDownloadStart: onDownloadStart,
    );
  }
}

/// Handles notification of JavaScript dialogs, favicons, titles, and the
/// progress.
///
/// See https://developer.android.com/reference/android/webkit/WebChromeClient.
class WebChromeClient extends PigeonInternalProxyApiBaseClass {
  WebChromeClient({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    this.onProgressChanged,
    required this.onShowFileChooser,
    this.onPermissionRequest,
    this.onShowCustomView,
    this.onHideCustomView,
    this.onGeolocationPermissionsShowPrompt,
    this.onGeolocationPermissionsHidePrompt,
    this.onConsoleMessage,
    this.onJsAlert,
    required this.onJsConfirm,
    this.onJsPrompt,
  }) {
    final int pigeonVar_instanceIdentifier =
        pigeon_instanceManager.addDartCreatedInstance(this);
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebChromeClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.pigeon_defaultConstructor';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
    () async {
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
  }

  /// Constructs [WebChromeClient] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebChromeClient.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    this.onProgressChanged,
    required this.onShowFileChooser,
    this.onPermissionRequest,
    this.onShowCustomView,
    this.onHideCustomView,
    this.onGeolocationPermissionsShowPrompt,
    this.onGeolocationPermissionsHidePrompt,
    this.onConsoleMessage,
    this.onJsAlert,
    required this.onJsConfirm,
    this.onJsPrompt,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecWebChromeClient =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  /// Tell the host application the current progress of loading a page.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onProgressChanged: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebChromeClient pigeon_instance,
    WebView webView,
    int progress,
  )? onProgressChanged;

  /// Tell the client to show a file chooser.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onShowFileChooser: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final Future<List<String>> Function(
    WebChromeClient pigeon_instance,
    WebView webView,
    FileChooserParams params,
  ) onShowFileChooser;

  /// Notify the host application that web content is requesting permission to
  /// access the specified resources and the permission currently isn't granted
  /// or denied.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onPermissionRequest: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebChromeClient pigeon_instance,
    PermissionRequest request,
  )? onPermissionRequest;

  /// Callback to Dart function `WebChromeClient.onShowCustomView`.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onShowCustomView: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebChromeClient pigeon_instance,
    View view,
    CustomViewCallback callback,
  )? onShowCustomView;

  /// Notify the host application that the current page has entered full screen
  /// mode.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onHideCustomView: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(WebChromeClient pigeon_instance)? onHideCustomView;

  /// Notify the host application that web content from the specified origin is
  /// attempting to use the Geolocation API, but no permission state is
  /// currently set for that origin.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onGeolocationPermissionsShowPrompt: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebChromeClient pigeon_instance,
    String origin,
    GeolocationPermissionsCallback callback,
  )? onGeolocationPermissionsShowPrompt;

  /// Notify the host application that a request for Geolocation permissions,
  /// made with a previous call to `onGeolocationPermissionsShowPrompt` has been
  /// canceled.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onGeolocationPermissionsHidePrompt: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(WebChromeClient pigeon_instance)?
      onGeolocationPermissionsHidePrompt;

  /// Report a JavaScript console message to the host application.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onConsoleMessage: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final void Function(
    WebChromeClient pigeon_instance,
    ConsoleMessage message,
  )? onConsoleMessage;

  /// Notify the host application that the web page wants to display a
  /// JavaScript `alert()` dialog.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onJsAlert: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final Future<void> Function(
    WebChromeClient pigeon_instance,
    WebView webView,
    String url,
    String message,
  )? onJsAlert;

  /// Notify the host application that the web page wants to display a
  /// JavaScript `confirm()` dialog.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onJsConfirm: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final Future<bool> Function(
    WebChromeClient pigeon_instance,
    WebView webView,
    String url,
    String message,
  ) onJsConfirm;

  /// Notify the host application that the web page wants to display a
  /// JavaScript `prompt()` dialog.
  ///
  /// For the associated Native object to be automatically garbage collected,
  /// it is required that the implementation of this `Function` doesn't have a
  /// strong reference to the encapsulating class instance. When this `Function`
  /// references a non-local variable, it is strongly recommended to access it
  /// with a `WeakReference`:
  ///
  /// ```dart
  /// final WeakReference weakMyVariable = WeakReference(myVariable);
  /// final WebChromeClient instance = WebChromeClient(
  ///  onJsPrompt: (WebChromeClient pigeon_instance, ...) {
  ///    print(weakMyVariable?.target);
  ///  },
  /// );
  /// ```
  ///
  /// Alternatively, [PigeonInstanceManager.removeWeakReference] can be used to
  /// release the associated Native object manually.
  final Future<String?> Function(
    WebChromeClient pigeon_instance,
    WebView webView,
    String url,
    String message,
    String defaultValue,
  )? onJsPrompt;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    void Function(
      WebChromeClient pigeon_instance,
      WebView webView,
      int progress,
    )? onProgressChanged,
    Future<List<String>> Function(
      WebChromeClient pigeon_instance,
      WebView webView,
      FileChooserParams params,
    )? onShowFileChooser,
    void Function(
      WebChromeClient pigeon_instance,
      PermissionRequest request,
    )? onPermissionRequest,
    void Function(
      WebChromeClient pigeon_instance,
      View view,
      CustomViewCallback callback,
    )? onShowCustomView,
    void Function(WebChromeClient pigeon_instance)? onHideCustomView,
    void Function(
      WebChromeClient pigeon_instance,
      String origin,
      GeolocationPermissionsCallback callback,
    )? onGeolocationPermissionsShowPrompt,
    void Function(WebChromeClient pigeon_instance)?
        onGeolocationPermissionsHidePrompt,
    void Function(
      WebChromeClient pigeon_instance,
      ConsoleMessage message,
    )? onConsoleMessage,
    Future<void> Function(
      WebChromeClient pigeon_instance,
      WebView webView,
      String url,
      String message,
    )? onJsAlert,
    Future<bool> Function(
      WebChromeClient pigeon_instance,
      WebView webView,
      String url,
      String message,
    )? onJsConfirm,
    Future<String?> Function(
      WebChromeClient pigeon_instance,
      WebView webView,
      String url,
      String message,
      String defaultValue,
    )? onJsPrompt,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onProgressChanged',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onProgressChanged was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onProgressChanged was null, expected non-null WebChromeClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onProgressChanged was null, expected non-null WebView.');
          final int? arg_progress = (args[2] as int?);
          assert(arg_progress != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onProgressChanged was null, expected non-null int.');
          try {
            (onProgressChanged ?? arg_pigeon_instance!.onProgressChanged)
                ?.call(arg_pigeon_instance!, arg_webView!, arg_progress!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowFileChooser',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowFileChooser was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowFileChooser was null, expected non-null WebChromeClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowFileChooser was null, expected non-null WebView.');
          final FileChooserParams? arg_params = (args[2] as FileChooserParams?);
          assert(arg_params != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowFileChooser was null, expected non-null FileChooserParams.');
          try {
            final List<String> output = await (onShowFileChooser ??
                    arg_pigeon_instance!.onShowFileChooser)
                .call(arg_pigeon_instance!, arg_webView!, arg_params!);
            return wrapResponse(result: output);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onPermissionRequest',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onPermissionRequest was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onPermissionRequest was null, expected non-null WebChromeClient.');
          final PermissionRequest? arg_request =
              (args[1] as PermissionRequest?);
          assert(arg_request != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onPermissionRequest was null, expected non-null PermissionRequest.');
          try {
            (onPermissionRequest ?? arg_pigeon_instance!.onPermissionRequest)
                ?.call(arg_pigeon_instance!, arg_request!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowCustomView',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowCustomView was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowCustomView was null, expected non-null WebChromeClient.');
          final View? arg_view = (args[1] as View?);
          assert(arg_view != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowCustomView was null, expected non-null View.');
          final CustomViewCallback? arg_callback =
              (args[2] as CustomViewCallback?);
          assert(arg_callback != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowCustomView was null, expected non-null CustomViewCallback.');
          try {
            (onShowCustomView ?? arg_pigeon_instance!.onShowCustomView)
                ?.call(arg_pigeon_instance!, arg_view!, arg_callback!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onHideCustomView',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onHideCustomView was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onHideCustomView was null, expected non-null WebChromeClient.');
          try {
            (onHideCustomView ?? arg_pigeon_instance!.onHideCustomView)
                ?.call(arg_pigeon_instance!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsShowPrompt',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsShowPrompt was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsShowPrompt was null, expected non-null WebChromeClient.');
          final String? arg_origin = (args[1] as String?);
          assert(arg_origin != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsShowPrompt was null, expected non-null String.');
          final GeolocationPermissionsCallback? arg_callback =
              (args[2] as GeolocationPermissionsCallback?);
          assert(arg_callback != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsShowPrompt was null, expected non-null GeolocationPermissionsCallback.');
          try {
            (onGeolocationPermissionsShowPrompt ??
                    arg_pigeon_instance!.onGeolocationPermissionsShowPrompt)
                ?.call(arg_pigeon_instance!, arg_origin!, arg_callback!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsHidePrompt',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsHidePrompt was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsHidePrompt was null, expected non-null WebChromeClient.');
          try {
            (onGeolocationPermissionsHidePrompt ??
                    arg_pigeon_instance!.onGeolocationPermissionsHidePrompt)
                ?.call(arg_pigeon_instance!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onConsoleMessage',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onConsoleMessage was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onConsoleMessage was null, expected non-null WebChromeClient.');
          final ConsoleMessage? arg_message = (args[1] as ConsoleMessage?);
          assert(arg_message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onConsoleMessage was null, expected non-null ConsoleMessage.');
          try {
            (onConsoleMessage ?? arg_pigeon_instance!.onConsoleMessage)
                ?.call(arg_pigeon_instance!, arg_message!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert was null, expected non-null WebChromeClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert was null, expected non-null String.');
          final String? arg_message = (args[3] as String?);
          assert(arg_message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert was null, expected non-null String.');
          try {
            await (onJsAlert ?? arg_pigeon_instance!.onJsAlert)?.call(
                arg_pigeon_instance!, arg_webView!, arg_url!, arg_message!);
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm was null, expected non-null WebChromeClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm was null, expected non-null String.');
          final String? arg_message = (args[3] as String?);
          assert(arg_message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm was null, expected non-null String.');
          try {
            final bool output =
                await (onJsConfirm ?? arg_pigeon_instance!.onJsConfirm).call(
                    arg_pigeon_instance!, arg_webView!, arg_url!, arg_message!);
            return wrapResponse(result: output);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }

    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final WebChromeClient? arg_pigeon_instance =
              (args[0] as WebChromeClient?);
          assert(arg_pigeon_instance != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt was null, expected non-null WebChromeClient.');
          final WebView? arg_webView = (args[1] as WebView?);
          assert(arg_webView != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt was null, expected non-null WebView.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt was null, expected non-null String.');
          final String? arg_message = (args[3] as String?);
          assert(arg_message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt was null, expected non-null String.');
          final String? arg_defaultValue = (args[4] as String?);
          assert(arg_defaultValue != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt was null, expected non-null String.');
          try {
            final String? output =
                await (onJsPrompt ?? arg_pigeon_instance!.onJsPrompt)?.call(
                    arg_pigeon_instance!,
                    arg_webView!,
                    arg_url!,
                    arg_message!,
                    arg_defaultValue!);
            return wrapResponse(result: output);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Sets the required synchronous return value for the Java method,
  /// `WebChromeClient.onShowFileChooser(...)`.
  ///
  /// The Java method, `WebChromeClient.onShowFileChooser(...)`, requires
  /// a boolean to be returned and this method sets the returned value for all
  /// calls to the Java method.
  ///
  /// Setting this to true indicates that all file chooser requests should be
  /// handled by `onShowFileChooser` and the returned list of Strings will be
  /// returned to the WebView. Otherwise, the client will use the default
  /// handling and the returned value in `onShowFileChooser` will be ignored.
  ///
  /// Requires `onShowFileChooser` to be nonnull.
  ///
  /// Defaults to false.
  Future<void> setSynchronousReturnValueForOnShowFileChooser(bool value) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebChromeClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnShowFileChooser';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the required synchronous return value for the Java method,
  /// `WebChromeClient.onConsoleMessage(...)`.
  ///
  /// The Java method, `WebChromeClient.onConsoleMessage(...)`, requires
  /// a boolean to be returned and this method sets the returned value for all
  /// calls to the Java method.
  ///
  /// Setting this to true indicates that the client is handling all console
  /// messages.
  ///
  /// Requires `onConsoleMessage` to be nonnull.
  ///
  /// Defaults to false.
  Future<void> setSynchronousReturnValueForOnConsoleMessage(bool value) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebChromeClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnConsoleMessage';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the required synchronous return value for the Java method,
  /// `WebChromeClient.onJsAlert(...)`.
  ///
  /// The Java method, `WebChromeClient.onJsAlert(...)`, requires a boolean to
  /// be returned and this method sets the returned value for all calls to the
  /// Java method.
  ///
  /// Setting this to true indicates that the client is handling all console
  /// messages.
  ///
  /// Requires `onJsAlert` to be nonnull.
  ///
  /// Defaults to false.
  Future<void> setSynchronousReturnValueForOnJsAlert(bool value) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebChromeClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnJsAlert';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the required synchronous return value for the Java method,
  /// `WebChromeClient.onJsConfirm(...)`.
  ///
  /// The Java method, `WebChromeClient.onJsConfirm(...)`, requires a boolean to
  /// be returned and this method sets the returned value for all calls to the
  /// Java method.
  ///
  /// Setting this to true indicates that the client is handling all console
  /// messages.
  ///
  /// Requires `onJsConfirm` to be nonnull.
  ///
  /// Defaults to false.
  Future<void> setSynchronousReturnValueForOnJsConfirm(bool value) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebChromeClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnJsConfirm';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Sets the required synchronous return value for the Java method,
  /// `WebChromeClient.onJsPrompt(...)`.
  ///
  /// The Java method, `WebChromeClient.onJsPrompt(...)`, requires a boolean to
  /// be returned and this method sets the returned value for all calls to the
  /// Java method.
  ///
  /// Setting this to true indicates that the client is handling all console
  /// messages.
  ///
  /// Requires `onJsPrompt` to be nonnull.
  ///
  /// Defaults to false.
  Future<void> setSynchronousReturnValueForOnJsPrompt(bool value) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebChromeClient;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnJsPrompt';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, value]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  WebChromeClient pigeon_copy() {
    return WebChromeClient.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      onProgressChanged: onProgressChanged,
      onShowFileChooser: onShowFileChooser,
      onPermissionRequest: onPermissionRequest,
      onShowCustomView: onShowCustomView,
      onHideCustomView: onHideCustomView,
      onGeolocationPermissionsShowPrompt: onGeolocationPermissionsShowPrompt,
      onGeolocationPermissionsHidePrompt: onGeolocationPermissionsHidePrompt,
      onConsoleMessage: onConsoleMessage,
      onJsAlert: onJsAlert,
      onJsConfirm: onJsConfirm,
      onJsPrompt: onJsPrompt,
    );
  }
}

/// Provides access to the assets registered as part of the App bundle.
///
/// Convenience class for accessing Flutter asset resources.
class FlutterAssetManager extends PigeonInternalProxyApiBaseClass {
  /// Constructs [FlutterAssetManager] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  FlutterAssetManager.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecFlutterAssetManager =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  /// The global instance of the `FlutterAssetManager`.
  static final FlutterAssetManager instance = pigeonVar_instance();

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    FlutterAssetManager Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  FlutterAssetManager.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  static FlutterAssetManager pigeonVar_instance() {
    final FlutterAssetManager pigeonVar_instance =
        FlutterAssetManager.pigeon_detached();
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(PigeonInstanceManager.instance);
    final BinaryMessenger pigeonVar_binaryMessenger =
        ServicesBinding.instance.defaultBinaryMessenger;
    final int pigeonVar_instanceIdentifier = PigeonInstanceManager.instance
        .addDartCreatedInstance(pigeonVar_instance);
    () async {
      const String pigeonVar_channelName =
          'dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.instance';
      final BasicMessageChannel<Object?> pigeonVar_channel =
          BasicMessageChannel<Object?>(
        pigeonVar_channelName,
        pigeonChannelCodec,
        binaryMessenger: pigeonVar_binaryMessenger,
      );
      final Future<Object?> pigeonVar_sendFuture =
          pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
    return pigeonVar_instance;
  }

  /// Returns a String array of all the assets at the given path.
  ///
  /// Throws an IOException in case I/O operations were interrupted.
  Future<List<String>> list(String path) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecFlutterAssetManager;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.list';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, path]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<String>();
    }
  }

  /// Gets the relative file path to the Flutter asset with the given name, including the file's
  /// extension, e.g., "myImage.jpg".
  ///
  /// The returned file path is relative to the Android app's standard asset's
  /// directory. Therefore, the returned path is appropriate to pass to
  /// Android's AssetManager, but the path is not appropriate to load as an
  /// absolute path.
  Future<String> getAssetFilePathByName(String name) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecFlutterAssetManager;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.getAssetFilePathByName';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, name]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as String?)!;
    }
  }

  @override
  FlutterAssetManager pigeon_copy() {
    return FlutterAssetManager.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// This class is used to manage the JavaScript storage APIs provided by the
/// WebView.
///
/// See https://developer.android.com/reference/android/webkit/WebStorage.
class WebStorage extends PigeonInternalProxyApiBaseClass {
  /// Constructs [WebStorage] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  WebStorage.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecWebStorage =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static final WebStorage instance = pigeonVar_instance();

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    WebStorage Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.WebStorage.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebStorage.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.WebStorage.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  WebStorage.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  static WebStorage pigeonVar_instance() {
    final WebStorage pigeonVar_instance = WebStorage.pigeon_detached();
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(PigeonInstanceManager.instance);
    final BinaryMessenger pigeonVar_binaryMessenger =
        ServicesBinding.instance.defaultBinaryMessenger;
    final int pigeonVar_instanceIdentifier = PigeonInstanceManager.instance
        .addDartCreatedInstance(pigeonVar_instance);
    () async {
      const String pigeonVar_channelName =
          'dev.flutter.pigeon.webview_flutter_android.WebStorage.instance';
      final BasicMessageChannel<Object?> pigeonVar_channel =
          BasicMessageChannel<Object?>(
        pigeonVar_channelName,
        pigeonChannelCodec,
        binaryMessenger: pigeonVar_binaryMessenger,
      );
      final Future<Object?> pigeonVar_sendFuture =
          pigeonVar_channel.send(<Object?>[pigeonVar_instanceIdentifier]);
      final List<Object?>? pigeonVar_replyList =
          await pigeonVar_sendFuture as List<Object?>?;
      if (pigeonVar_replyList == null) {
        throw _createConnectionError(pigeonVar_channelName);
      } else if (pigeonVar_replyList.length > 1) {
        throw PlatformException(
          code: pigeonVar_replyList[0]! as String,
          message: pigeonVar_replyList[1] as String?,
          details: pigeonVar_replyList[2],
        );
      } else {
        return;
      }
    }();
    return pigeonVar_instance;
  }

  /// Clears all storage currently being used by the JavaScript storage APIs.
  Future<void> deleteAllData() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecWebStorage;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.WebStorage.deleteAllData';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  WebStorage pigeon_copy() {
    return WebStorage.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// Parameters used in the `WebChromeClient.onShowFileChooser` method.
///
/// See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams.
class FileChooserParams extends PigeonInternalProxyApiBaseClass {
  /// Constructs [FileChooserParams] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  FileChooserParams.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.isCaptureEnabled,
    required this.acceptTypes,
    required this.mode,
    this.filenameHint,
  });

  /// Preference for a live media captured value (e.g. Camera, Microphone).
  final bool isCaptureEnabled;

  /// An array of acceptable MIME types.
  final List<String> acceptTypes;

  /// File chooser mode.
  final FileChooserMode mode;

  /// File name of a default selection if specified, or null.
  final String? filenameHint;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    FileChooserParams Function(
      bool isCaptureEnabled,
      List<String> acceptTypes,
      FileChooserMode mode,
      String? filenameHint,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance was null, expected non-null int.');
          final bool? arg_isCaptureEnabled = (args[1] as bool?);
          assert(arg_isCaptureEnabled != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance was null, expected non-null bool.');
          final List<String>? arg_acceptTypes =
              (args[2] as List<Object?>?)?.cast<String>();
          assert(arg_acceptTypes != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance was null, expected non-null List<String>.');
          final FileChooserMode? arg_mode = (args[3] as FileChooserMode?);
          assert(arg_mode != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance was null, expected non-null FileChooserMode.');
          final String? arg_filenameHint = (args[4] as String?);
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_isCaptureEnabled!, arg_acceptTypes!,
                      arg_mode!, arg_filenameHint) ??
                  FileChooserParams.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    isCaptureEnabled: arg_isCaptureEnabled!,
                    acceptTypes: arg_acceptTypes!,
                    mode: arg_mode!,
                    filenameHint: arg_filenameHint,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  FileChooserParams pigeon_copy() {
    return FileChooserParams.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      isCaptureEnabled: isCaptureEnabled,
      acceptTypes: acceptTypes,
      mode: mode,
      filenameHint: filenameHint,
    );
  }
}

/// This class defines a permission request and is used when web content
/// requests access to protected resources.
///
/// See https://developer.android.com/reference/android/webkit/PermissionRequest.
class PermissionRequest extends PigeonInternalProxyApiBaseClass {
  /// Constructs [PermissionRequest] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  PermissionRequest.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.resources,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecPermissionRequest =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  final List<String> resources;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    PermissionRequest Function(List<String> resources)? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.PermissionRequest.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PermissionRequest.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PermissionRequest.pigeon_newInstance was null, expected non-null int.');
          final List<String>? arg_resources =
              (args[1] as List<Object?>?)?.cast<String>();
          assert(arg_resources != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PermissionRequest.pigeon_newInstance was null, expected non-null List<String>.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_resources!) ??
                  PermissionRequest.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    resources: arg_resources!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Call this method to grant origin the permission to access the given
  /// resources.
  Future<void> grant(List<String> resources) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecPermissionRequest;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.PermissionRequest.grant';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, resources]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Call this method to deny the request.
  Future<void> deny() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecPermissionRequest;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.PermissionRequest.deny';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  PermissionRequest pigeon_copy() {
    return PermissionRequest.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      resources: resources,
    );
  }
}

/// A callback interface used by the host application to notify the current page
/// that its custom view has been dismissed.
///
/// See https://developer.android.com/reference/android/webkit/WebChromeClient.CustomViewCallback.
class CustomViewCallback extends PigeonInternalProxyApiBaseClass {
  /// Constructs [CustomViewCallback] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  CustomViewCallback.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecCustomViewCallback =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    CustomViewCallback Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.CustomViewCallback.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.CustomViewCallback.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.CustomViewCallback.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  CustomViewCallback.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Invoked when the host application dismisses the custom view.
  Future<void> onCustomViewHidden() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecCustomViewCallback;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.CustomViewCallback.onCustomViewHidden';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  CustomViewCallback pigeon_copy() {
    return CustomViewCallback.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// This class represents the basic building block for user interface
/// components.
///
/// See https://developer.android.com/reference/android/view/View.
class View extends PigeonInternalProxyApiBaseClass {
  /// Constructs [View] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  View.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecView =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    View Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.View.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.View.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.View.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  View.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Set the scrolled position of your view.
  Future<void> scrollTo(
    int x,
    int y,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.View.scrollTo';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, x, y]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Move the scrolled position of your view.
  Future<void> scrollBy(
    int x,
    int y,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.View.scrollBy';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, x, y]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Return the scrolled position of this view.
  Future<WebViewPoint> getScrollPosition() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecView;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.View.getScrollPosition';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as WebViewPoint?)!;
    }
  }

  @override
  View pigeon_copy() {
    return View.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// A callback interface used by the host application to set the Geolocation
/// permission state for an origin.
///
/// See https://developer.android.com/reference/android/webkit/GeolocationPermissions.Callback.
class GeolocationPermissionsCallback extends PigeonInternalProxyApiBaseClass {
  /// Constructs [GeolocationPermissionsCallback] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  GeolocationPermissionsCallback.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecGeolocationPermissionsCallback =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    GeolocationPermissionsCallback Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallback.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallback.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallback.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  GeolocationPermissionsCallback.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Sets the Geolocation permission state for the supplied origin.
  Future<void> invoke(
    String origin,
    bool allow,
    bool retain,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecGeolocationPermissionsCallback;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallback.invoke';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, origin, allow, retain]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  GeolocationPermissionsCallback pigeon_copy() {
    return GeolocationPermissionsCallback.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// Represents a request for HTTP authentication.
///
/// See https://developer.android.com/reference/android/webkit/HttpAuthHandler.
class HttpAuthHandler extends PigeonInternalProxyApiBaseClass {
  /// Constructs [HttpAuthHandler] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  HttpAuthHandler.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecHttpAuthHandler =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    HttpAuthHandler Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  HttpAuthHandler.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Gets whether the credentials stored for the current host (i.e. the host
  /// for which `WebViewClient.onReceivedHttpAuthRequest` was called) are
  /// suitable for use.
  Future<bool> useHttpAuthUsernamePassword() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecHttpAuthHandler;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.useHttpAuthUsernamePassword';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  /// Instructs the WebView to cancel the authentication request..
  Future<void> cancel() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecHttpAuthHandler;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.cancel';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Instructs the WebView to proceed with the authentication with the given
  /// credentials.
  Future<void> proceed(
    String username,
    String password,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecHttpAuthHandler;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.proceed';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, username, password]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  HttpAuthHandler pigeon_copy() {
    return HttpAuthHandler.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// Defines a message containing a description and arbitrary data object that
/// can be sent to a `Handler`.
///
/// See https://developer.android.com/reference/android/os/Message.
class AndroidMessage extends PigeonInternalProxyApiBaseClass {
  /// Constructs [AndroidMessage] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  AndroidMessage.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecAndroidMessage =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    AndroidMessage Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.AndroidMessage.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.AndroidMessage.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.AndroidMessage.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  AndroidMessage.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Sends this message to the Android native `Handler` specified by
  /// getTarget().
  ///
  /// Throws a null pointer exception if this field has not been set.
  Future<void> sendToTarget() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecAndroidMessage;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.AndroidMessage.sendToTarget';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  AndroidMessage pigeon_copy() {
    return AndroidMessage.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// Defines a message containing a description and arbitrary data object that
/// can be sent to a `Handler`.
///
/// See https://developer.android.com/reference/android/webkit/ClientCertRequest.
class ClientCertRequest extends PigeonInternalProxyApiBaseClass {
  /// Constructs [ClientCertRequest] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  ClientCertRequest.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecClientCertRequest =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    ClientCertRequest Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  ClientCertRequest.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Cancel this request.
  Future<void> cancel() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecClientCertRequest;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.cancel';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Ignore the request for now.
  Future<void> ignore() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecClientCertRequest;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.ignore';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Proceed with the specified private key and client certificate chain.
  Future<void> proceed(
    PrivateKey privateKey,
    List<X509Certificate> chain,
  ) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecClientCertRequest;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.proceed';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, privateKey, chain]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  ClientCertRequest pigeon_copy() {
    return ClientCertRequest.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// A private key.
///
/// The purpose of this interface is to group (and provide type safety for) all
/// private key interfaces.
///
/// See https://developer.android.com/reference/java/security/PrivateKey.
class PrivateKey extends PigeonInternalProxyApiBaseClass {
  /// Constructs [PrivateKey] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  PrivateKey.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    PrivateKey Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.PrivateKey.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PrivateKey.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.PrivateKey.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  PrivateKey.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  PrivateKey pigeon_copy() {
    return PrivateKey.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// Abstract class for X.509 certificates.
///
/// This provides a standard way to access all the attributes of an X.509
/// certificate.
///
/// See https://developer.android.com/reference/java/security/cert/X509Certificate.
class X509Certificate extends PigeonInternalProxyApiBaseClass {
  /// Constructs [X509Certificate] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  X509Certificate.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    X509Certificate Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.X509Certificate.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.X509Certificate.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.X509Certificate.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  X509Certificate.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  @override
  X509Certificate pigeon_copy() {
    return X509Certificate.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// Represents a request for handling an SSL error.
///
/// See https://developer.android.com/reference/android/webkit/SslErrorHandler.
class SslErrorHandler extends PigeonInternalProxyApiBaseClass {
  /// Constructs [SslErrorHandler] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  SslErrorHandler.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecSslErrorHandler =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    SslErrorHandler Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  SslErrorHandler.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Instructs the WebView that encountered the SSL certificate error to
  /// terminate communication with the server.
  Future<void> cancel() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslErrorHandler;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.cancel';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Instructs the WebView that encountered the SSL certificate error to ignore
  /// the error and continue communicating with the server.
  Future<void> proceed() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslErrorHandler;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.proceed';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  @override
  SslErrorHandler pigeon_copy() {
    return SslErrorHandler.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// This class represents a set of one or more SSL errors and the associated SSL
/// certificate.
///
/// See https://developer.android.com/reference/android/net/http/SslError.
class SslError extends PigeonInternalProxyApiBaseClass {
  /// Constructs [SslError] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  SslError.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
    required this.certificate,
    required this.url,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecSslError =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  /// Gets the SSL certificate associated with this object.
  final SslCertificate certificate;

  /// Gets the URL associated with this object.
  final String url;

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    SslError Function(
      SslCertificate certificate,
      String url,
    )? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.SslError.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslError.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslError.pigeon_newInstance was null, expected non-null int.');
          final SslCertificate? arg_certificate = (args[1] as SslCertificate?);
          assert(arg_certificate != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslError.pigeon_newInstance was null, expected non-null SslCertificate.');
          final String? arg_url = (args[2] as String?);
          assert(arg_url != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslError.pigeon_newInstance was null, expected non-null String.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call(arg_certificate!, arg_url!) ??
                  SslError.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                    certificate: arg_certificate!,
                    url: arg_url!,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Gets the most severe SSL error in this object's set of errors.
  Future<SslErrorType> getPrimaryError() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslError;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslError.getPrimaryError';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as SslErrorType?)!;
    }
  }

  /// Determines whether this object includes the supplied error.
  Future<bool> hasError(SslErrorType error) async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslError;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslError.hasError';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this, error]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  @override
  SslError pigeon_copy() {
    return SslError.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
      certificate: certificate,
      url: url,
    );
  }
}

/// A distinguished name helper class.
///
/// A 3-tuple of:
/// the most specific common name (CN)
/// the most specific organization (O)
/// the most specific organizational unit (OU)
class SslCertificateDName extends PigeonInternalProxyApiBaseClass {
  /// Constructs [SslCertificateDName] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  SslCertificateDName.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec
      _pigeonVar_codecSslCertificateDName =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    SslCertificateDName Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  SslCertificateDName.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// The most specific Common-name (CN) component of this name.
  Future<String> getCName() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificateDName;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getCName';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as String?)!;
    }
  }

  /// The distinguished name (normally includes CN, O, and OU names).
  Future<String> getDName() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificateDName;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getDName';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as String?)!;
    }
  }

  /// The most specific Organization (O) component of this name.
  Future<String> getOName() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificateDName;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getOName';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as String?)!;
    }
  }

  /// The most specific Organizational Unit (OU) component of this name.
  Future<String> getUName() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificateDName;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getUName';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as String?)!;
    }
  }

  @override
  SslCertificateDName pigeon_copy() {
    return SslCertificateDName.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

/// SSL certificate info (certificate details) class.
///
/// See https://developer.android.com/reference/android/net/http/SslCertificate.
class SslCertificate extends PigeonInternalProxyApiBaseClass {
  /// Constructs [SslCertificate] without creating the associated native object.
  ///
  /// This should only be used by subclasses created by this library or to
  /// create copies for an [PigeonInstanceManager].
  @protected
  SslCertificate.pigeon_detached({
    super.pigeon_binaryMessenger,
    super.pigeon_instanceManager,
  });

  late final _PigeonInternalProxyApiBaseCodec _pigeonVar_codecSslCertificate =
      _PigeonInternalProxyApiBaseCodec(pigeon_instanceManager);

  static void pigeon_setUpMessageHandlers({
    bool pigeon_clearHandlers = false,
    BinaryMessenger? pigeon_binaryMessenger,
    PigeonInstanceManager? pigeon_instanceManager,
    SslCertificate Function()? pigeon_newInstance,
  }) {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _PigeonInternalProxyApiBaseCodec(
            pigeon_instanceManager ?? PigeonInstanceManager.instance);
    final BinaryMessenger? binaryMessenger = pigeon_binaryMessenger;
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.webview_flutter_android.SslCertificate.pigeon_newInstance',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (pigeon_clearHandlers) {
        pigeonVar_channel.setMessageHandler(null);
      } else {
        pigeonVar_channel.setMessageHandler((Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslCertificate.pigeon_newInstance was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final int? arg_pigeon_instanceIdentifier = (args[0] as int?);
          assert(arg_pigeon_instanceIdentifier != null,
              'Argument for dev.flutter.pigeon.webview_flutter_android.SslCertificate.pigeon_newInstance was null, expected non-null int.');
          try {
            (pigeon_instanceManager ?? PigeonInstanceManager.instance)
                .addHostCreatedInstance(
              pigeon_newInstance?.call() ??
                  SslCertificate.pigeon_detached(
                    pigeon_binaryMessenger: pigeon_binaryMessenger,
                    pigeon_instanceManager: pigeon_instanceManager,
                  ),
              arg_pigeon_instanceIdentifier!,
            );
            return wrapResponse(empty: true);
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }

  /// Issued-by distinguished name or null if none has been set.
  Future<SslCertificateDName?> getIssuedBy() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificate;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificate.getIssuedBy';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as SslCertificateDName?);
    }
  }

  /// Issued-to distinguished name or null if none has been set.
  Future<SslCertificateDName?> getIssuedTo() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificate;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificate.getIssuedTo';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as SslCertificateDName?);
    }
  }

  /// Not-after date from the certificate validity period or null if none has been
  /// set.
  Future<int?> getValidNotAfterMsSinceEpoch() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificate;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificate.getValidNotAfterMsSinceEpoch';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as int?);
    }
  }

  /// Not-before date from the certificate validity period or null if none has
  /// been set.
  Future<int?> getValidNotBeforeMsSinceEpoch() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificate;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificate.getValidNotBeforeMsSinceEpoch';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as int?);
    }
  }

  /// The X509Certificate used to create this SslCertificate or null if no
  /// certificate was provided.
  ///
  /// Always returns null on Android versions below Q.
  Future<X509Certificate?> getX509Certificate() async {
    final _PigeonInternalProxyApiBaseCodec pigeonChannelCodec =
        _pigeonVar_codecSslCertificate;
    final BinaryMessenger? pigeonVar_binaryMessenger = pigeon_binaryMessenger;
    const String pigeonVar_channelName =
        'dev.flutter.pigeon.webview_flutter_android.SslCertificate.getX509Certificate';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final Future<Object?> pigeonVar_sendFuture =
        pigeonVar_channel.send(<Object?>[this]);
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_sendFuture as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as X509Certificate?);
    }
  }

  @override
  SslCertificate pigeon_copy() {
    return SslCertificate.pigeon_detached(
      pigeon_binaryMessenger: pigeon_binaryMessenger,
      pigeon_instanceManager: pigeon_instanceManager,
    );
  }
}

