name: webview_flutter_android
description: A Flutter plugin that provides a WebView widget on Android.
repository: https://github.com/flutter/packages/tree/main/packages/webview_flutter/webview_flutter_android
issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+webview%22
version: 4.3.5

environment:
  sdk: ^3.6.0
  flutter: ">=3.27.0"

flutter:
  plugin:
    implements: webview_flutter
    platforms:
      android:
        package: io.flutter.plugins.webviewflutter
        pluginClass: WebViewFlutterPlugin
        dartPluginClass: AndroidWebViewPlatform

dependencies:
  flutter:
    sdk: flutter
  webview_flutter_platform_interface: ^2.10.0

dev_dependencies:
  build_runner: ^2.1.4
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  pigeon: ^25.2.0

topics:
  - html
  - webview
  - webview-flutter
