<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="io.flutter.plugins.webviewflutterexample">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <application
        android:icon="@mipmap/ic_launcher"
        android:label="webview_flutter_example"
        android:usesCleartextTraffic="true">
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <activity
            android:name="io.flutter.embedding.android.FlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

    <!-- The INTERNET permission is required for development. Specifically,
         flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- When tests are ran on Firebase Test Lab, a wake lock
         permission failure prevents tests from running.
    -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Used to debug accessing the camera in a WebView -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
</manifest>
