// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in webview_flutter_android/test/android_webview_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:typed_data' as _i13;
import 'dart:ui' as _i4;

import 'package:flutter/foundation.dart' as _i10;
import 'package:flutter/gestures.dart' as _i11;
import 'package:flutter/services.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i12;
import 'package:webview_flutter_android/src/android_proxy.dart' as _i9;
import 'package:webview_flutter_android/src/android_webkit.g.dart' as _i2;
import 'package:webview_flutter_android/src/android_webview_controller.dart'
    as _i7;
import 'package:webview_flutter_android/src/platform_views_service_proxy.dart'
    as _i5;
import 'package:webview_flutter_platform_interface/webview_flutter_platform_interface.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeWebChromeClient_0 extends _i1.SmartFake
    implements _i2.WebChromeClient {
  _FakeWebChromeClient_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeWebViewClient_1 extends _i1.SmartFake implements _i2.WebViewClient {
  _FakeWebViewClient_1(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeDownloadListener_2 extends _i1.SmartFake
    implements _i2.DownloadListener {
  _FakeDownloadListener_2(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakePlatformNavigationDelegateCreationParams_3 extends _i1.SmartFake
    implements _i3.PlatformNavigationDelegateCreationParams {
  _FakePlatformNavigationDelegateCreationParams_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakePlatformWebViewControllerCreationParams_4 extends _i1.SmartFake
    implements _i3.PlatformWebViewControllerCreationParams {
  _FakePlatformWebViewControllerCreationParams_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeObject_5 extends _i1.SmartFake implements Object {
  _FakeObject_5(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeOffset_6 extends _i1.SmartFake implements _i4.Offset {
  _FakeOffset_6(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeWebView_7 extends _i1.SmartFake implements _i2.WebView {
  _FakeWebView_7(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeJavaScriptChannel_8 extends _i1.SmartFake
    implements _i2.JavaScriptChannel {
  _FakeJavaScriptChannel_8(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeCookieManager_9 extends _i1.SmartFake implements _i2.CookieManager {
  _FakeCookieManager_9(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeFlutterAssetManager_10 extends _i1.SmartFake
    implements _i2.FlutterAssetManager {
  _FakeFlutterAssetManager_10(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeWebStorage_11 extends _i1.SmartFake implements _i2.WebStorage {
  _FakeWebStorage_11(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakePigeonInstanceManager_12 extends _i1.SmartFake
    implements _i2.PigeonInstanceManager {
  _FakePigeonInstanceManager_12(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakePlatformViewsServiceProxy_13 extends _i1.SmartFake
    implements _i5.PlatformViewsServiceProxy {
  _FakePlatformViewsServiceProxy_13(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakePlatformWebViewController_14 extends _i1.SmartFake
    implements _i3.PlatformWebViewController {
  _FakePlatformWebViewController_14(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeSize_15 extends _i1.SmartFake implements _i4.Size {
  _FakeSize_15(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeGeolocationPermissionsCallback_16 extends _i1.SmartFake
    implements _i2.GeolocationPermissionsCallback {
  _FakeGeolocationPermissionsCallback_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakePermissionRequest_17 extends _i1.SmartFake
    implements _i2.PermissionRequest {
  _FakePermissionRequest_17(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeExpensiveAndroidViewController_18 extends _i1.SmartFake
    implements _i6.ExpensiveAndroidViewController {
  _FakeExpensiveAndroidViewController_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeSurfaceAndroidViewController_19 extends _i1.SmartFake
    implements _i6.SurfaceAndroidViewController {
  _FakeSurfaceAndroidViewController_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeWebSettings_20 extends _i1.SmartFake implements _i2.WebSettings {
  _FakeWebSettings_20(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeWebViewPoint_21 extends _i1.SmartFake implements _i2.WebViewPoint {
  _FakeWebViewPoint_21(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

/// A class which mocks [AndroidNavigationDelegate].
///
/// See the documentation for Mockito's code generation for more information.
class MockAndroidNavigationDelegate extends _i1.Mock
    implements _i7.AndroidNavigationDelegate {
  @override
  _i2.WebChromeClient get androidWebChromeClient => (super.noSuchMethod(
        Invocation.getter(#androidWebChromeClient),
        returnValue: _FakeWebChromeClient_0(
          this,
          Invocation.getter(#androidWebChromeClient),
        ),
        returnValueForMissingStub: _FakeWebChromeClient_0(
          this,
          Invocation.getter(#androidWebChromeClient),
        ),
      ) as _i2.WebChromeClient);

  @override
  _i2.WebViewClient get androidWebViewClient => (super.noSuchMethod(
        Invocation.getter(#androidWebViewClient),
        returnValue: _FakeWebViewClient_1(
          this,
          Invocation.getter(#androidWebViewClient),
        ),
        returnValueForMissingStub: _FakeWebViewClient_1(
          this,
          Invocation.getter(#androidWebViewClient),
        ),
      ) as _i2.WebViewClient);

  @override
  _i2.DownloadListener get androidDownloadListener => (super.noSuchMethod(
        Invocation.getter(#androidDownloadListener),
        returnValue: _FakeDownloadListener_2(
          this,
          Invocation.getter(#androidDownloadListener),
        ),
        returnValueForMissingStub: _FakeDownloadListener_2(
          this,
          Invocation.getter(#androidDownloadListener),
        ),
      ) as _i2.DownloadListener);

  @override
  _i3.PlatformNavigationDelegateCreationParams get params =>
      (super.noSuchMethod(
        Invocation.getter(#params),
        returnValue: _FakePlatformNavigationDelegateCreationParams_3(
          this,
          Invocation.getter(#params),
        ),
        returnValueForMissingStub:
            _FakePlatformNavigationDelegateCreationParams_3(
          this,
          Invocation.getter(#params),
        ),
      ) as _i3.PlatformNavigationDelegateCreationParams);

  @override
  _i8.Future<void> setOnLoadRequest(_i7.LoadRequestCallback? onLoadRequest) =>
      (super.noSuchMethod(
        Invocation.method(#setOnLoadRequest, [onLoadRequest]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnNavigationRequest(
    _i3.NavigationRequestCallback? onNavigationRequest,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnNavigationRequest, [onNavigationRequest]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnPageStarted(_i3.PageEventCallback? onPageStarted) =>
      (super.noSuchMethod(
        Invocation.method(#setOnPageStarted, [onPageStarted]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnPageFinished(_i3.PageEventCallback? onPageFinished) =>
      (super.noSuchMethod(
        Invocation.method(#setOnPageFinished, [onPageFinished]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnHttpError(_i3.HttpResponseErrorCallback? onHttpError) =>
      (super.noSuchMethod(
        Invocation.method(#setOnHttpError, [onHttpError]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnProgress(_i3.ProgressCallback? onProgress) =>
      (super.noSuchMethod(
        Invocation.method(#setOnProgress, [onProgress]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnWebResourceError(
    _i3.WebResourceErrorCallback? onWebResourceError,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnWebResourceError, [onWebResourceError]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnUrlChange(_i3.UrlChangeCallback? onUrlChange) =>
      (super.noSuchMethod(
        Invocation.method(#setOnUrlChange, [onUrlChange]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnHttpAuthRequest(
    _i3.HttpAuthRequestCallback? onHttpAuthRequest,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnHttpAuthRequest, [onHttpAuthRequest]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);
}

/// A class which mocks [AndroidWebViewController].
///
/// See the documentation for Mockito's code generation for more information.
class MockAndroidWebViewController extends _i1.Mock
    implements _i7.AndroidWebViewController {
  @override
  int get webViewIdentifier => (super.noSuchMethod(
        Invocation.getter(#webViewIdentifier),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  _i3.PlatformWebViewControllerCreationParams get params => (super.noSuchMethod(
        Invocation.getter(#params),
        returnValue: _FakePlatformWebViewControllerCreationParams_4(
          this,
          Invocation.getter(#params),
        ),
        returnValueForMissingStub:
            _FakePlatformWebViewControllerCreationParams_4(
          this,
          Invocation.getter(#params),
        ),
      ) as _i3.PlatformWebViewControllerCreationParams);

  @override
  _i8.Future<void> setAllowFileAccess(bool? allow) => (super.noSuchMethod(
        Invocation.method(#setAllowFileAccess, [allow]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> loadFile(String? absoluteFilePath) => (super.noSuchMethod(
        Invocation.method(#loadFile, [absoluteFilePath]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> loadFlutterAsset(String? key) => (super.noSuchMethod(
        Invocation.method(#loadFlutterAsset, [key]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> loadHtmlString(String? html, {String? baseUrl}) =>
      (super.noSuchMethod(
        Invocation.method(#loadHtmlString, [html], {#baseUrl: baseUrl}),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> loadRequest(_i3.LoadRequestParams? params) =>
      (super.noSuchMethod(
        Invocation.method(#loadRequest, [params]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<String?> currentUrl() => (super.noSuchMethod(
        Invocation.method(#currentUrl, []),
        returnValue: _i8.Future<String?>.value(),
        returnValueForMissingStub: _i8.Future<String?>.value(),
      ) as _i8.Future<String?>);

  @override
  _i8.Future<bool> canGoBack() => (super.noSuchMethod(
        Invocation.method(#canGoBack, []),
        returnValue: _i8.Future<bool>.value(false),
        returnValueForMissingStub: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> canGoForward() => (super.noSuchMethod(
        Invocation.method(#canGoForward, []),
        returnValue: _i8.Future<bool>.value(false),
        returnValueForMissingStub: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<void> goBack() => (super.noSuchMethod(
        Invocation.method(#goBack, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> goForward() => (super.noSuchMethod(
        Invocation.method(#goForward, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(#reload, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(#clearCache, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> clearLocalStorage() => (super.noSuchMethod(
        Invocation.method(#clearLocalStorage, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setPlatformNavigationDelegate(
    _i3.PlatformNavigationDelegate? handler,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setPlatformNavigationDelegate, [handler]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> runJavaScript(String? javaScript) => (super.noSuchMethod(
        Invocation.method(#runJavaScript, [javaScript]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<Object> runJavaScriptReturningResult(String? javaScript) =>
      (super.noSuchMethod(
        Invocation.method(#runJavaScriptReturningResult, [javaScript]),
        returnValue: _i8.Future<Object>.value(
          _FakeObject_5(
            this,
            Invocation.method(#runJavaScriptReturningResult, [javaScript]),
          ),
        ),
        returnValueForMissingStub: _i8.Future<Object>.value(
          _FakeObject_5(
            this,
            Invocation.method(#runJavaScriptReturningResult, [javaScript]),
          ),
        ),
      ) as _i8.Future<Object>);

  @override
  _i8.Future<void> addJavaScriptChannel(
    _i3.JavaScriptChannelParams? javaScriptChannelParams,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#addJavaScriptChannel, [javaScriptChannelParams]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> removeJavaScriptChannel(String? javaScriptChannelName) =>
      (super.noSuchMethod(
        Invocation.method(#removeJavaScriptChannel, [
          javaScriptChannelName,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<String?> getTitle() => (super.noSuchMethod(
        Invocation.method(#getTitle, []),
        returnValue: _i8.Future<String?>.value(),
        returnValueForMissingStub: _i8.Future<String?>.value(),
      ) as _i8.Future<String?>);

  @override
  _i8.Future<void> scrollTo(int? x, int? y) => (super.noSuchMethod(
        Invocation.method(#scrollTo, [x, y]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> scrollBy(int? x, int? y) => (super.noSuchMethod(
        Invocation.method(#scrollBy, [x, y]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<_i4.Offset> getScrollPosition() => (super.noSuchMethod(
        Invocation.method(#getScrollPosition, []),
        returnValue: _i8.Future<_i4.Offset>.value(
          _FakeOffset_6(this, Invocation.method(#getScrollPosition, [])),
        ),
        returnValueForMissingStub: _i8.Future<_i4.Offset>.value(
          _FakeOffset_6(this, Invocation.method(#getScrollPosition, [])),
        ),
      ) as _i8.Future<_i4.Offset>);

  @override
  _i8.Future<void> enableZoom(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#enableZoom, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setBackgroundColor(_i4.Color? color) => (super.noSuchMethod(
        Invocation.method(#setBackgroundColor, [color]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setJavaScriptMode(_i3.JavaScriptMode? javaScriptMode) =>
      (super.noSuchMethod(
        Invocation.method(#setJavaScriptMode, [javaScriptMode]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setUserAgent(String? userAgent) => (super.noSuchMethod(
        Invocation.method(#setUserAgent, [userAgent]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnScrollPositionChange(
    void Function(_i3.ScrollPositionChange)? onScrollPositionChange,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnScrollPositionChange, [
          onScrollPositionChange,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setMediaPlaybackRequiresUserGesture(bool? require) =>
      (super.noSuchMethod(
        Invocation.method(#setMediaPlaybackRequiresUserGesture, [require]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setTextZoom(int? textZoom) => (super.noSuchMethod(
        Invocation.method(#setTextZoom, [textZoom]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setAllowContentAccess(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setAllowContentAccess, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setGeolocationEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setGeolocationEnabled, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnShowFileSelector(
    _i8.Future<List<String>> Function(_i7.FileSelectorParams)?
        onShowFileSelector,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnShowFileSelector, [onShowFileSelector]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnPlatformPermissionRequest(
    void Function(_i3.PlatformWebViewPermissionRequest)? onPermissionRequest,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnPlatformPermissionRequest, [
          onPermissionRequest,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setGeolocationPermissionsPromptCallbacks({
    _i7.OnGeolocationPermissionsShowPrompt? onShowPrompt,
    _i7.OnGeolocationPermissionsHidePrompt? onHidePrompt,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#setGeolocationPermissionsPromptCallbacks, [], {
          #onShowPrompt: onShowPrompt,
          #onHidePrompt: onHidePrompt,
        }),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setCustomWidgetCallbacks({
    required _i7.OnShowCustomWidgetCallback? onShowCustomWidget,
    required _i7.OnHideCustomWidgetCallback? onHideCustomWidget,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#setCustomWidgetCallbacks, [], {
          #onShowCustomWidget: onShowCustomWidget,
          #onHideCustomWidget: onHideCustomWidget,
        }),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnConsoleMessage(
    void Function(_i3.JavaScriptConsoleMessage)? onConsoleMessage,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnConsoleMessage, [onConsoleMessage]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<String?> getUserAgent() => (super.noSuchMethod(
        Invocation.method(#getUserAgent, []),
        returnValue: _i8.Future<String?>.value(),
        returnValueForMissingStub: _i8.Future<String?>.value(),
      ) as _i8.Future<String?>);

  @override
  _i8.Future<void> setOnJavaScriptAlertDialog(
    _i8.Future<void> Function(_i3.JavaScriptAlertDialogRequest)?
        onJavaScriptAlertDialog,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnJavaScriptAlertDialog, [
          onJavaScriptAlertDialog,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnJavaScriptConfirmDialog(
    _i8.Future<bool> Function(_i3.JavaScriptConfirmDialogRequest)?
        onJavaScriptConfirmDialog,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnJavaScriptConfirmDialog, [
          onJavaScriptConfirmDialog,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setOnJavaScriptTextInputDialog(
    _i8.Future<String> Function(_i3.JavaScriptTextInputDialogRequest)?
        onJavaScriptTextInputDialog,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setOnJavaScriptTextInputDialog, [
          onJavaScriptTextInputDialog,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);
}

/// A class which mocks [AndroidWebViewProxy].
///
/// See the documentation for Mockito's code generation for more information.
class MockAndroidWebViewProxy extends _i1.Mock
    implements _i9.AndroidWebViewProxy {
  @override
  _i2.WebView Function({
    void Function(_i2.WebView, int, int, int, int)? onScrollChanged,
  }) get newWebView => (super.noSuchMethod(
        Invocation.getter(#newWebView),
        returnValue: ({
          void Function(_i2.WebView, int, int, int, int)? onScrollChanged,
        }) =>
            _FakeWebView_7(this, Invocation.getter(#newWebView)),
        returnValueForMissingStub: ({
          void Function(_i2.WebView, int, int, int, int)? onScrollChanged,
        }) =>
            _FakeWebView_7(this, Invocation.getter(#newWebView)),
      ) as _i2.WebView Function({
        void Function(_i2.WebView, int, int, int, int)? onScrollChanged,
      }));

  @override
  _i2.JavaScriptChannel Function({
    required String channelName,
    required void Function(_i2.JavaScriptChannel, String) postMessage,
  }) get newJavaScriptChannel => (super.noSuchMethod(
        Invocation.getter(#newJavaScriptChannel),
        returnValue: ({
          required String channelName,
          required void Function(_i2.JavaScriptChannel, String) postMessage,
        }) =>
            _FakeJavaScriptChannel_8(
          this,
          Invocation.getter(#newJavaScriptChannel),
        ),
        returnValueForMissingStub: ({
          required String channelName,
          required void Function(_i2.JavaScriptChannel, String) postMessage,
        }) =>
            _FakeJavaScriptChannel_8(
          this,
          Invocation.getter(#newJavaScriptChannel),
        ),
      ) as _i2.JavaScriptChannel Function({
        required String channelName,
        required void Function(_i2.JavaScriptChannel, String) postMessage,
      }));

  @override
  _i2.WebViewClient Function({
    void Function(_i2.WebViewClient, _i2.WebView, String, bool)?
        doUpdateVisitedHistory,
    void Function(
      _i2.WebViewClient,
      _i2.WebView,
      _i2.AndroidMessage,
      _i2.AndroidMessage,
    )? onFormResubmission,
    void Function(_i2.WebViewClient, _i2.WebView, String)? onLoadResource,
    void Function(_i2.WebViewClient, _i2.WebView, String)? onPageCommitVisible,
    void Function(_i2.WebViewClient, _i2.WebView, String)? onPageFinished,
    void Function(_i2.WebViewClient, _i2.WebView, String)? onPageStarted,
    void Function(_i2.WebViewClient, _i2.WebView, _i2.ClientCertRequest)?
        onReceivedClientCertRequest,
    void Function(_i2.WebViewClient, _i2.WebView, int, String, String)?
        onReceivedError,
    void Function(
      _i2.WebViewClient,
      _i2.WebView,
      _i2.HttpAuthHandler,
      String,
      String,
    )? onReceivedHttpAuthRequest,
    void Function(
      _i2.WebViewClient,
      _i2.WebView,
      _i2.WebResourceRequest,
      _i2.WebResourceResponse,
    )? onReceivedHttpError,
    void Function(_i2.WebViewClient, _i2.WebView, String, String?, String)?
        onReceivedLoginRequest,
    void Function(
      _i2.WebViewClient,
      _i2.WebView,
      _i2.WebResourceRequest,
      _i2.WebResourceError,
    )? onReceivedRequestError,
    void Function(
      _i2.WebViewClient,
      _i2.WebView,
      _i2.WebResourceRequest,
      _i2.WebResourceErrorCompat,
    )? onReceivedRequestErrorCompat,
    void Function(
      _i2.WebViewClient,
      _i2.WebView,
      _i2.SslErrorHandler,
      _i2.SslError,
    )? onReceivedSslError,
    void Function(_i2.WebViewClient, _i2.WebView, double, double)?
        onScaleChanged,
    void Function(_i2.WebViewClient, _i2.WebView, _i2.WebResourceRequest)?
        requestLoading,
    void Function(_i2.WebViewClient, _i2.WebView, String)? urlLoading,
  }) get newWebViewClient => (super.noSuchMethod(
        Invocation.getter(#newWebViewClient),
        returnValue: ({
          void Function(_i2.WebViewClient, _i2.WebView, String, bool)?
              doUpdateVisitedHistory,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.AndroidMessage,
            _i2.AndroidMessage,
          )? onFormResubmission,
          void Function(_i2.WebViewClient, _i2.WebView, String)? onLoadResource,
          void Function(_i2.WebViewClient, _i2.WebView, String)?
              onPageCommitVisible,
          void Function(_i2.WebViewClient, _i2.WebView, String)? onPageFinished,
          void Function(_i2.WebViewClient, _i2.WebView, String)? onPageStarted,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.ClientCertRequest,
          )? onReceivedClientCertRequest,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            int,
            String,
            String,
          )? onReceivedError,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.HttpAuthHandler,
            String,
            String,
          )? onReceivedHttpAuthRequest,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
            _i2.WebResourceResponse,
          )? onReceivedHttpError,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            String,
            String?,
            String,
          )? onReceivedLoginRequest,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
            _i2.WebResourceError,
          )? onReceivedRequestError,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
            _i2.WebResourceErrorCompat,
          )? onReceivedRequestErrorCompat,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.SslErrorHandler,
            _i2.SslError,
          )? onReceivedSslError,
          void Function(_i2.WebViewClient, _i2.WebView, double, double)?
              onScaleChanged,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
          )? requestLoading,
          void Function(_i2.WebViewClient, _i2.WebView, String)? urlLoading,
        }) =>
            _FakeWebViewClient_1(
          this,
          Invocation.getter(#newWebViewClient),
        ),
        returnValueForMissingStub: ({
          void Function(_i2.WebViewClient, _i2.WebView, String, bool)?
              doUpdateVisitedHistory,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.AndroidMessage,
            _i2.AndroidMessage,
          )? onFormResubmission,
          void Function(_i2.WebViewClient, _i2.WebView, String)? onLoadResource,
          void Function(_i2.WebViewClient, _i2.WebView, String)?
              onPageCommitVisible,
          void Function(_i2.WebViewClient, _i2.WebView, String)? onPageFinished,
          void Function(_i2.WebViewClient, _i2.WebView, String)? onPageStarted,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.ClientCertRequest,
          )? onReceivedClientCertRequest,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            int,
            String,
            String,
          )? onReceivedError,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.HttpAuthHandler,
            String,
            String,
          )? onReceivedHttpAuthRequest,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
            _i2.WebResourceResponse,
          )? onReceivedHttpError,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            String,
            String?,
            String,
          )? onReceivedLoginRequest,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
            _i2.WebResourceError,
          )? onReceivedRequestError,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
            _i2.WebResourceErrorCompat,
          )? onReceivedRequestErrorCompat,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.SslErrorHandler,
            _i2.SslError,
          )? onReceivedSslError,
          void Function(_i2.WebViewClient, _i2.WebView, double, double)?
              onScaleChanged,
          void Function(
            _i2.WebViewClient,
            _i2.WebView,
            _i2.WebResourceRequest,
          )? requestLoading,
          void Function(_i2.WebViewClient, _i2.WebView, String)? urlLoading,
        }) =>
            _FakeWebViewClient_1(
          this,
          Invocation.getter(#newWebViewClient),
        ),
      ) as _i2.WebViewClient Function({
        void Function(_i2.WebViewClient, _i2.WebView, String, bool)?
            doUpdateVisitedHistory,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.AndroidMessage,
          _i2.AndroidMessage,
        )? onFormResubmission,
        void Function(_i2.WebViewClient, _i2.WebView, String)? onLoadResource,
        void Function(_i2.WebViewClient, _i2.WebView, String)?
            onPageCommitVisible,
        void Function(_i2.WebViewClient, _i2.WebView, String)? onPageFinished,
        void Function(_i2.WebViewClient, _i2.WebView, String)? onPageStarted,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.ClientCertRequest,
        )? onReceivedClientCertRequest,
        void Function(_i2.WebViewClient, _i2.WebView, int, String, String)?
            onReceivedError,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.HttpAuthHandler,
          String,
          String,
        )? onReceivedHttpAuthRequest,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.WebResourceRequest,
          _i2.WebResourceResponse,
        )? onReceivedHttpError,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          String,
          String?,
          String,
        )? onReceivedLoginRequest,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.WebResourceRequest,
          _i2.WebResourceError,
        )? onReceivedRequestError,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.WebResourceRequest,
          _i2.WebResourceErrorCompat,
        )? onReceivedRequestErrorCompat,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.SslErrorHandler,
          _i2.SslError,
        )? onReceivedSslError,
        void Function(_i2.WebViewClient, _i2.WebView, double, double)?
            onScaleChanged,
        void Function(
          _i2.WebViewClient,
          _i2.WebView,
          _i2.WebResourceRequest,
        )? requestLoading,
        void Function(_i2.WebViewClient, _i2.WebView, String)? urlLoading,
      }));

  @override
  _i2.DownloadListener Function({
    required void Function(
      _i2.DownloadListener,
      String,
      String,
      String,
      String,
      int,
    ) onDownloadStart,
  }) get newDownloadListener => (super.noSuchMethod(
        Invocation.getter(#newDownloadListener),
        returnValue: ({
          required void Function(
            _i2.DownloadListener,
            String,
            String,
            String,
            String,
            int,
          ) onDownloadStart,
        }) =>
            _FakeDownloadListener_2(
          this,
          Invocation.getter(#newDownloadListener),
        ),
        returnValueForMissingStub: ({
          required void Function(
            _i2.DownloadListener,
            String,
            String,
            String,
            String,
            int,
          ) onDownloadStart,
        }) =>
            _FakeDownloadListener_2(
          this,
          Invocation.getter(#newDownloadListener),
        ),
      ) as _i2.DownloadListener Function({
        required void Function(
          _i2.DownloadListener,
          String,
          String,
          String,
          String,
          int,
        ) onDownloadStart,
      }));

  @override
  _i2.WebChromeClient Function({
    required _i8.Future<bool> Function(
      _i2.WebChromeClient,
      _i2.WebView,
      String,
      String,
    ) onJsConfirm,
    required _i8.Future<List<String>> Function(
      _i2.WebChromeClient,
      _i2.WebView,
      _i2.FileChooserParams,
    ) onShowFileChooser,
    void Function(_i2.WebChromeClient, _i2.ConsoleMessage)? onConsoleMessage,
    void Function(_i2.WebChromeClient)? onGeolocationPermissionsHidePrompt,
    void Function(
      _i2.WebChromeClient,
      String,
      _i2.GeolocationPermissionsCallback,
    )? onGeolocationPermissionsShowPrompt,
    void Function(_i2.WebChromeClient)? onHideCustomView,
    _i8.Future<void> Function(_i2.WebChromeClient, _i2.WebView, String, String)?
        onJsAlert,
    _i8.Future<String?> Function(
      _i2.WebChromeClient,
      _i2.WebView,
      String,
      String,
      String,
    )? onJsPrompt,
    void Function(_i2.WebChromeClient, _i2.PermissionRequest)?
        onPermissionRequest,
    void Function(_i2.WebChromeClient, _i2.WebView, int)? onProgressChanged,
    void Function(_i2.WebChromeClient, _i2.View, _i2.CustomViewCallback)?
        onShowCustomView,
  }) get newWebChromeClient => (super.noSuchMethod(
        Invocation.getter(#newWebChromeClient),
        returnValue: ({
          void Function(_i2.WebChromeClient, _i2.ConsoleMessage)?
              onConsoleMessage,
          void Function(_i2.WebChromeClient)?
              onGeolocationPermissionsHidePrompt,
          void Function(
            _i2.WebChromeClient,
            String,
            _i2.GeolocationPermissionsCallback,
          )? onGeolocationPermissionsShowPrompt,
          void Function(_i2.WebChromeClient)? onHideCustomView,
          _i8.Future<void> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
          )? onJsAlert,
          required _i8.Future<bool> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
          ) onJsConfirm,
          _i8.Future<String?> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
            String,
          )? onJsPrompt,
          void Function(_i2.WebChromeClient, _i2.PermissionRequest)?
              onPermissionRequest,
          void Function(_i2.WebChromeClient, _i2.WebView, int)?
              onProgressChanged,
          void Function(
            _i2.WebChromeClient,
            _i2.View,
            _i2.CustomViewCallback,
          )? onShowCustomView,
          required _i8.Future<List<String>> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            _i2.FileChooserParams,
          ) onShowFileChooser,
        }) =>
            _FakeWebChromeClient_0(
          this,
          Invocation.getter(#newWebChromeClient),
        ),
        returnValueForMissingStub: ({
          void Function(_i2.WebChromeClient, _i2.ConsoleMessage)?
              onConsoleMessage,
          void Function(_i2.WebChromeClient)?
              onGeolocationPermissionsHidePrompt,
          void Function(
            _i2.WebChromeClient,
            String,
            _i2.GeolocationPermissionsCallback,
          )? onGeolocationPermissionsShowPrompt,
          void Function(_i2.WebChromeClient)? onHideCustomView,
          _i8.Future<void> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
          )? onJsAlert,
          required _i8.Future<bool> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
          ) onJsConfirm,
          _i8.Future<String?> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
            String,
          )? onJsPrompt,
          void Function(_i2.WebChromeClient, _i2.PermissionRequest)?
              onPermissionRequest,
          void Function(_i2.WebChromeClient, _i2.WebView, int)?
              onProgressChanged,
          void Function(
            _i2.WebChromeClient,
            _i2.View,
            _i2.CustomViewCallback,
          )? onShowCustomView,
          required _i8.Future<List<String>> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            _i2.FileChooserParams,
          ) onShowFileChooser,
        }) =>
            _FakeWebChromeClient_0(
          this,
          Invocation.getter(#newWebChromeClient),
        ),
      ) as _i2.WebChromeClient Function({
        required _i8.Future<bool> Function(
          _i2.WebChromeClient,
          _i2.WebView,
          String,
          String,
        ) onJsConfirm,
        required _i8.Future<List<String>> Function(
          _i2.WebChromeClient,
          _i2.WebView,
          _i2.FileChooserParams,
        ) onShowFileChooser,
        void Function(_i2.WebChromeClient, _i2.ConsoleMessage)?
            onConsoleMessage,
        void Function(_i2.WebChromeClient)? onGeolocationPermissionsHidePrompt,
        void Function(
          _i2.WebChromeClient,
          String,
          _i2.GeolocationPermissionsCallback,
        )? onGeolocationPermissionsShowPrompt,
        void Function(_i2.WebChromeClient)? onHideCustomView,
        _i8.Future<void> Function(
          _i2.WebChromeClient,
          _i2.WebView,
          String,
          String,
        )? onJsAlert,
        _i8.Future<String?> Function(
          _i2.WebChromeClient,
          _i2.WebView,
          String,
          String,
          String,
        )? onJsPrompt,
        void Function(_i2.WebChromeClient, _i2.PermissionRequest)?
            onPermissionRequest,
        void Function(_i2.WebChromeClient, _i2.WebView, int)? onProgressChanged,
        void Function(
          _i2.WebChromeClient,
          _i2.View,
          _i2.CustomViewCallback,
        )? onShowCustomView,
      }));

  @override
  _i8.Future<void> Function(bool) get setWebContentsDebuggingEnabledWebView =>
      (super.noSuchMethod(
        Invocation.getter(#setWebContentsDebuggingEnabledWebView),
        returnValue: (bool __p0) => _i8.Future<void>.value(),
        returnValueForMissingStub: (bool __p0) => _i8.Future<void>.value(),
      ) as _i8.Future<void> Function(bool));

  @override
  _i2.CookieManager Function() get instanceCookieManager => (super.noSuchMethod(
        Invocation.getter(#instanceCookieManager),
        returnValue: () => _FakeCookieManager_9(
          this,
          Invocation.getter(#instanceCookieManager),
        ),
        returnValueForMissingStub: () => _FakeCookieManager_9(
          this,
          Invocation.getter(#instanceCookieManager),
        ),
      ) as _i2.CookieManager Function());

  @override
  _i2.FlutterAssetManager Function() get instanceFlutterAssetManager =>
      (super.noSuchMethod(
        Invocation.getter(#instanceFlutterAssetManager),
        returnValue: () => _FakeFlutterAssetManager_10(
          this,
          Invocation.getter(#instanceFlutterAssetManager),
        ),
        returnValueForMissingStub: () => _FakeFlutterAssetManager_10(
          this,
          Invocation.getter(#instanceFlutterAssetManager),
        ),
      ) as _i2.FlutterAssetManager Function());

  @override
  _i2.WebStorage Function() get instanceWebStorage => (super.noSuchMethod(
        Invocation.getter(#instanceWebStorage),
        returnValue: () => _FakeWebStorage_11(
          this,
          Invocation.getter(#instanceWebStorage),
        ),
        returnValueForMissingStub: () => _FakeWebStorage_11(
          this,
          Invocation.getter(#instanceWebStorage),
        ),
      ) as _i2.WebStorage Function());
}

/// A class which mocks [AndroidWebViewWidgetCreationParams].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockAndroidWebViewWidgetCreationParams extends _i1.Mock
    implements _i7.AndroidWebViewWidgetCreationParams {
  @override
  _i2.PigeonInstanceManager get instanceManager => (super.noSuchMethod(
        Invocation.getter(#instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i5.PlatformViewsServiceProxy get platformViewsServiceProxy =>
      (super.noSuchMethod(
        Invocation.getter(#platformViewsServiceProxy),
        returnValue: _FakePlatformViewsServiceProxy_13(
          this,
          Invocation.getter(#platformViewsServiceProxy),
        ),
        returnValueForMissingStub: _FakePlatformViewsServiceProxy_13(
          this,
          Invocation.getter(#platformViewsServiceProxy),
        ),
      ) as _i5.PlatformViewsServiceProxy);

  @override
  bool get displayWithHybridComposition => (super.noSuchMethod(
        Invocation.getter(#displayWithHybridComposition),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i3.PlatformWebViewController get controller => (super.noSuchMethod(
        Invocation.getter(#controller),
        returnValue: _FakePlatformWebViewController_14(
          this,
          Invocation.getter(#controller),
        ),
        returnValueForMissingStub: _FakePlatformWebViewController_14(
          this,
          Invocation.getter(#controller),
        ),
      ) as _i3.PlatformWebViewController);

  @override
  _i4.TextDirection get layoutDirection => (super.noSuchMethod(
        Invocation.getter(#layoutDirection),
        returnValue: _i4.TextDirection.rtl,
        returnValueForMissingStub: _i4.TextDirection.rtl,
      ) as _i4.TextDirection);

  @override
  Set<_i10.Factory<_i11.OneSequenceGestureRecognizer>> get gestureRecognizers =>
      (super.noSuchMethod(
        Invocation.getter(#gestureRecognizers),
        returnValue: <_i10.Factory<_i11.OneSequenceGestureRecognizer>>{},
        returnValueForMissingStub: <_i10
            .Factory<_i11.OneSequenceGestureRecognizer>>{},
      ) as Set<_i10.Factory<_i11.OneSequenceGestureRecognizer>>);
}

/// A class which mocks [ExpensiveAndroidViewController].
///
/// See the documentation for Mockito's code generation for more information.
class MockExpensiveAndroidViewController extends _i1.Mock
    implements _i6.ExpensiveAndroidViewController {
  @override
  bool get requiresViewComposition => (super.noSuchMethod(
        Invocation.getter(#requiresViewComposition),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get viewId => (super.noSuchMethod(
        Invocation.getter(#viewId),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  bool get awaitingCreation => (super.noSuchMethod(
        Invocation.getter(#awaitingCreation),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i6.PointTransformer get pointTransformer => (super.noSuchMethod(
        Invocation.getter(#pointTransformer),
        returnValue: (_i4.Offset position) =>
            _FakeOffset_6(this, Invocation.getter(#pointTransformer)),
        returnValueForMissingStub: (_i4.Offset position) =>
            _FakeOffset_6(this, Invocation.getter(#pointTransformer)),
      ) as _i6.PointTransformer);

  @override
  set pointTransformer(_i6.PointTransformer? transformer) => super.noSuchMethod(
        Invocation.setter(#pointTransformer, transformer),
        returnValueForMissingStub: null,
      );

  @override
  bool get isCreated => (super.noSuchMethod(
        Invocation.getter(#isCreated),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  List<_i6.PlatformViewCreatedCallback> get createdCallbacks =>
      (super.noSuchMethod(
        Invocation.getter(#createdCallbacks),
        returnValue: <_i6.PlatformViewCreatedCallback>[],
        returnValueForMissingStub: <_i6.PlatformViewCreatedCallback>[],
      ) as List<_i6.PlatformViewCreatedCallback>);

  @override
  _i8.Future<void> setOffset(_i4.Offset? off) => (super.noSuchMethod(
        Invocation.method(#setOffset, [off]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> create({_i4.Size? size, _i4.Offset? position}) =>
      (super.noSuchMethod(
        Invocation.method(#create, [], {#size: size, #position: position}),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<_i4.Size> setSize(_i4.Size? size) => (super.noSuchMethod(
        Invocation.method(#setSize, [size]),
        returnValue: _i8.Future<_i4.Size>.value(
          _FakeSize_15(this, Invocation.method(#setSize, [size])),
        ),
        returnValueForMissingStub: _i8.Future<_i4.Size>.value(
          _FakeSize_15(this, Invocation.method(#setSize, [size])),
        ),
      ) as _i8.Future<_i4.Size>);

  @override
  _i8.Future<void> sendMotionEvent(_i6.AndroidMotionEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(#sendMotionEvent, [event]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  void addOnPlatformViewCreatedListener(
    _i6.PlatformViewCreatedCallback? listener,
  ) =>
      super.noSuchMethod(
        Invocation.method(#addOnPlatformViewCreatedListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  void removeOnPlatformViewCreatedListener(
    _i6.PlatformViewCreatedCallback? listener,
  ) =>
      super.noSuchMethod(
        Invocation.method(#removeOnPlatformViewCreatedListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  _i8.Future<void> setLayoutDirection(_i4.TextDirection? layoutDirection) =>
      (super.noSuchMethod(
        Invocation.method(#setLayoutDirection, [layoutDirection]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> dispatchPointerEvent(_i11.PointerEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(#dispatchPointerEvent, [event]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> clearFocus() => (super.noSuchMethod(
        Invocation.method(#clearFocus, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(#dispose, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);
}

/// A class which mocks [FlutterAssetManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterAssetManager extends _i1.Mock
    implements _i2.FlutterAssetManager {
  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<List<String>> list(String? path) => (super.noSuchMethod(
        Invocation.method(#list, [path]),
        returnValue: _i8.Future<List<String>>.value(<String>[]),
        returnValueForMissingStub: _i8.Future<List<String>>.value(
          <String>[],
        ),
      ) as _i8.Future<List<String>>);

  @override
  _i8.Future<String> getAssetFilePathByName(String? name) =>
      (super.noSuchMethod(
        Invocation.method(#getAssetFilePathByName, [name]),
        returnValue: _i8.Future<String>.value(
          _i12.dummyValue<String>(
            this,
            Invocation.method(#getAssetFilePathByName, [name]),
          ),
        ),
        returnValueForMissingStub: _i8.Future<String>.value(
          _i12.dummyValue<String>(
            this,
            Invocation.method(#getAssetFilePathByName, [name]),
          ),
        ),
      ) as _i8.Future<String>);

  @override
  _i2.FlutterAssetManager pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeFlutterAssetManager_10(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeFlutterAssetManager_10(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.FlutterAssetManager);
}

/// A class which mocks [GeolocationPermissionsCallback].
///
/// See the documentation for Mockito's code generation for more information.
class MockGeolocationPermissionsCallback extends _i1.Mock
    implements _i2.GeolocationPermissionsCallback {
  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<void> invoke(String? origin, bool? allow, bool? retain) =>
      (super.noSuchMethod(
        Invocation.method(#invoke, [origin, allow, retain]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i2.GeolocationPermissionsCallback pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeGeolocationPermissionsCallback_16(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeGeolocationPermissionsCallback_16(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.GeolocationPermissionsCallback);
}

/// A class which mocks [JavaScriptChannel].
///
/// See the documentation for Mockito's code generation for more information.
class MockJavaScriptChannel extends _i1.Mock implements _i2.JavaScriptChannel {
  @override
  String get channelName => (super.noSuchMethod(
        Invocation.getter(#channelName),
        returnValue: _i12.dummyValue<String>(
          this,
          Invocation.getter(#channelName),
        ),
        returnValueForMissingStub: _i12.dummyValue<String>(
          this,
          Invocation.getter(#channelName),
        ),
      ) as String);

  @override
  void Function(_i2.JavaScriptChannel, String) get postMessage =>
      (super.noSuchMethod(
        Invocation.getter(#postMessage),
        returnValue: (_i2.JavaScriptChannel pigeon_instance, String message) {},
        returnValueForMissingStub:
            (_i2.JavaScriptChannel pigeon_instance, String message) {},
      ) as void Function(_i2.JavaScriptChannel, String));

  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i2.JavaScriptChannel pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeJavaScriptChannel_8(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeJavaScriptChannel_8(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.JavaScriptChannel);
}

/// A class which mocks [PermissionRequest].
///
/// See the documentation for Mockito's code generation for more information.
class MockPermissionRequest extends _i1.Mock implements _i2.PermissionRequest {
  @override
  List<String> get resources => (super.noSuchMethod(
        Invocation.getter(#resources),
        returnValue: <String>[],
        returnValueForMissingStub: <String>[],
      ) as List<String>);

  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<void> grant(List<String>? resources) => (super.noSuchMethod(
        Invocation.method(#grant, [resources]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> deny() => (super.noSuchMethod(
        Invocation.method(#deny, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i2.PermissionRequest pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakePermissionRequest_17(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakePermissionRequest_17(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.PermissionRequest);
}

/// A class which mocks [PlatformViewsServiceProxy].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockPlatformViewsServiceProxy extends _i1.Mock
    implements _i5.PlatformViewsServiceProxy {
  @override
  _i6.ExpensiveAndroidViewController initExpensiveAndroidView({
    required int? id,
    required String? viewType,
    required _i4.TextDirection? layoutDirection,
    dynamic creationParams,
    _i6.MessageCodec<dynamic>? creationParamsCodec,
    _i4.VoidCallback? onFocus,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#initExpensiveAndroidView, [], {
          #id: id,
          #viewType: viewType,
          #layoutDirection: layoutDirection,
          #creationParams: creationParams,
          #creationParamsCodec: creationParamsCodec,
          #onFocus: onFocus,
        }),
        returnValue: _FakeExpensiveAndroidViewController_18(
          this,
          Invocation.method(#initExpensiveAndroidView, [], {
            #id: id,
            #viewType: viewType,
            #layoutDirection: layoutDirection,
            #creationParams: creationParams,
            #creationParamsCodec: creationParamsCodec,
            #onFocus: onFocus,
          }),
        ),
        returnValueForMissingStub: _FakeExpensiveAndroidViewController_18(
          this,
          Invocation.method(#initExpensiveAndroidView, [], {
            #id: id,
            #viewType: viewType,
            #layoutDirection: layoutDirection,
            #creationParams: creationParams,
            #creationParamsCodec: creationParamsCodec,
            #onFocus: onFocus,
          }),
        ),
      ) as _i6.ExpensiveAndroidViewController);

  @override
  _i6.SurfaceAndroidViewController initSurfaceAndroidView({
    required int? id,
    required String? viewType,
    required _i4.TextDirection? layoutDirection,
    dynamic creationParams,
    _i6.MessageCodec<dynamic>? creationParamsCodec,
    _i4.VoidCallback? onFocus,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#initSurfaceAndroidView, [], {
          #id: id,
          #viewType: viewType,
          #layoutDirection: layoutDirection,
          #creationParams: creationParams,
          #creationParamsCodec: creationParamsCodec,
          #onFocus: onFocus,
        }),
        returnValue: _FakeSurfaceAndroidViewController_19(
          this,
          Invocation.method(#initSurfaceAndroidView, [], {
            #id: id,
            #viewType: viewType,
            #layoutDirection: layoutDirection,
            #creationParams: creationParams,
            #creationParamsCodec: creationParamsCodec,
            #onFocus: onFocus,
          }),
        ),
        returnValueForMissingStub: _FakeSurfaceAndroidViewController_19(
          this,
          Invocation.method(#initSurfaceAndroidView, [], {
            #id: id,
            #viewType: viewType,
            #layoutDirection: layoutDirection,
            #creationParams: creationParams,
            #creationParamsCodec: creationParamsCodec,
            #onFocus: onFocus,
          }),
        ),
      ) as _i6.SurfaceAndroidViewController);
}

/// A class which mocks [SurfaceAndroidViewController].
///
/// See the documentation for Mockito's code generation for more information.
class MockSurfaceAndroidViewController extends _i1.Mock
    implements _i6.SurfaceAndroidViewController {
  @override
  bool get requiresViewComposition => (super.noSuchMethod(
        Invocation.getter(#requiresViewComposition),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  int get viewId => (super.noSuchMethod(
        Invocation.getter(#viewId),
        returnValue: 0,
        returnValueForMissingStub: 0,
      ) as int);

  @override
  bool get awaitingCreation => (super.noSuchMethod(
        Invocation.getter(#awaitingCreation),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i6.PointTransformer get pointTransformer => (super.noSuchMethod(
        Invocation.getter(#pointTransformer),
        returnValue: (_i4.Offset position) =>
            _FakeOffset_6(this, Invocation.getter(#pointTransformer)),
        returnValueForMissingStub: (_i4.Offset position) =>
            _FakeOffset_6(this, Invocation.getter(#pointTransformer)),
      ) as _i6.PointTransformer);

  @override
  set pointTransformer(_i6.PointTransformer? transformer) => super.noSuchMethod(
        Invocation.setter(#pointTransformer, transformer),
        returnValueForMissingStub: null,
      );

  @override
  bool get isCreated => (super.noSuchMethod(
        Invocation.getter(#isCreated),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  List<_i6.PlatformViewCreatedCallback> get createdCallbacks =>
      (super.noSuchMethod(
        Invocation.getter(#createdCallbacks),
        returnValue: <_i6.PlatformViewCreatedCallback>[],
        returnValueForMissingStub: <_i6.PlatformViewCreatedCallback>[],
      ) as List<_i6.PlatformViewCreatedCallback>);

  @override
  _i8.Future<void> setOffset(_i4.Offset? off) => (super.noSuchMethod(
        Invocation.method(#setOffset, [off]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> create({_i4.Size? size, _i4.Offset? position}) =>
      (super.noSuchMethod(
        Invocation.method(#create, [], {#size: size, #position: position}),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<_i4.Size> setSize(_i4.Size? size) => (super.noSuchMethod(
        Invocation.method(#setSize, [size]),
        returnValue: _i8.Future<_i4.Size>.value(
          _FakeSize_15(this, Invocation.method(#setSize, [size])),
        ),
        returnValueForMissingStub: _i8.Future<_i4.Size>.value(
          _FakeSize_15(this, Invocation.method(#setSize, [size])),
        ),
      ) as _i8.Future<_i4.Size>);

  @override
  _i8.Future<void> sendMotionEvent(_i6.AndroidMotionEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(#sendMotionEvent, [event]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  void addOnPlatformViewCreatedListener(
    _i6.PlatformViewCreatedCallback? listener,
  ) =>
      super.noSuchMethod(
        Invocation.method(#addOnPlatformViewCreatedListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  void removeOnPlatformViewCreatedListener(
    _i6.PlatformViewCreatedCallback? listener,
  ) =>
      super.noSuchMethod(
        Invocation.method(#removeOnPlatformViewCreatedListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  _i8.Future<void> setLayoutDirection(_i4.TextDirection? layoutDirection) =>
      (super.noSuchMethod(
        Invocation.method(#setLayoutDirection, [layoutDirection]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> dispatchPointerEvent(_i11.PointerEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(#dispatchPointerEvent, [event]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> clearFocus() => (super.noSuchMethod(
        Invocation.method(#clearFocus, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(#dispose, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);
}

/// A class which mocks [WebChromeClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebChromeClient extends _i1.Mock implements _i2.WebChromeClient {
  @override
  _i8.Future<List<String>> Function(
    _i2.WebChromeClient,
    _i2.WebView,
    _i2.FileChooserParams,
  ) get onShowFileChooser => (super.noSuchMethod(
        Invocation.getter(#onShowFileChooser),
        returnValue: (
          _i2.WebChromeClient pigeon_instance,
          _i2.WebView webView,
          _i2.FileChooserParams params,
        ) =>
            _i8.Future<List<String>>.value(<String>[]),
        returnValueForMissingStub: (
          _i2.WebChromeClient pigeon_instance,
          _i2.WebView webView,
          _i2.FileChooserParams params,
        ) =>
            _i8.Future<List<String>>.value(<String>[]),
      ) as _i8.Future<List<String>> Function(
        _i2.WebChromeClient,
        _i2.WebView,
        _i2.FileChooserParams,
      ));

  @override
  _i8.Future<bool> Function(_i2.WebChromeClient, _i2.WebView, String, String)
      get onJsConfirm => (super.noSuchMethod(
            Invocation.getter(#onJsConfirm),
            returnValue: (
              _i2.WebChromeClient pigeon_instance,
              _i2.WebView webView,
              String url,
              String message,
            ) =>
                _i8.Future<bool>.value(false),
            returnValueForMissingStub: (
              _i2.WebChromeClient pigeon_instance,
              _i2.WebView webView,
              String url,
              String message,
            ) =>
                _i8.Future<bool>.value(false),
          ) as _i8.Future<bool> Function(
            _i2.WebChromeClient,
            _i2.WebView,
            String,
            String,
          ));

  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<void> setSynchronousReturnValueForOnShowFileChooser(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(#setSynchronousReturnValueForOnShowFileChooser, [
          value,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setSynchronousReturnValueForOnConsoleMessage(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(#setSynchronousReturnValueForOnConsoleMessage, [
          value,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setSynchronousReturnValueForOnJsAlert(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(#setSynchronousReturnValueForOnJsAlert, [value]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setSynchronousReturnValueForOnJsConfirm(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(#setSynchronousReturnValueForOnJsConfirm, [
          value,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setSynchronousReturnValueForOnJsPrompt(bool? value) =>
      (super.noSuchMethod(
        Invocation.method(#setSynchronousReturnValueForOnJsPrompt, [value]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i2.WebChromeClient pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeWebChromeClient_0(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeWebChromeClient_0(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.WebChromeClient);
}

/// A class which mocks [WebSettings].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebSettings extends _i1.Mock implements _i2.WebSettings {
  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<void> setDomStorageEnabled(bool? flag) => (super.noSuchMethod(
        Invocation.method(#setDomStorageEnabled, [flag]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setJavaScriptCanOpenWindowsAutomatically(bool? flag) =>
      (super.noSuchMethod(
        Invocation.method(#setJavaScriptCanOpenWindowsAutomatically, [
          flag,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setSupportMultipleWindows(bool? support) =>
      (super.noSuchMethod(
        Invocation.method(#setSupportMultipleWindows, [support]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setJavaScriptEnabled(bool? flag) => (super.noSuchMethod(
        Invocation.method(#setJavaScriptEnabled, [flag]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setUserAgentString(String? userAgentString) =>
      (super.noSuchMethod(
        Invocation.method(#setUserAgentString, [userAgentString]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setMediaPlaybackRequiresUserGesture(bool? require) =>
      (super.noSuchMethod(
        Invocation.method(#setMediaPlaybackRequiresUserGesture, [require]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setSupportZoom(bool? support) => (super.noSuchMethod(
        Invocation.method(#setSupportZoom, [support]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setLoadWithOverviewMode(bool? overview) =>
      (super.noSuchMethod(
        Invocation.method(#setLoadWithOverviewMode, [overview]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setUseWideViewPort(bool? use) => (super.noSuchMethod(
        Invocation.method(#setUseWideViewPort, [use]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setDisplayZoomControls(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setDisplayZoomControls, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setBuiltInZoomControls(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setBuiltInZoomControls, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setAllowFileAccess(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setAllowFileAccess, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setAllowContentAccess(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setAllowContentAccess, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setGeolocationEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(#setGeolocationEnabled, [enabled]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setTextZoom(int? textZoom) => (super.noSuchMethod(
        Invocation.method(#setTextZoom, [textZoom]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<String> getUserAgentString() => (super.noSuchMethod(
        Invocation.method(#getUserAgentString, []),
        returnValue: _i8.Future<String>.value(
          _i12.dummyValue<String>(
            this,
            Invocation.method(#getUserAgentString, []),
          ),
        ),
        returnValueForMissingStub: _i8.Future<String>.value(
          _i12.dummyValue<String>(
            this,
            Invocation.method(#getUserAgentString, []),
          ),
        ),
      ) as _i8.Future<String>);

  @override
  _i2.WebSettings pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeWebSettings_20(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeWebSettings_20(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.WebSettings);
}

/// A class which mocks [WebView].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebView extends _i1.Mock implements _i2.WebView {
  @override
  _i2.WebSettings get settings => (super.noSuchMethod(
        Invocation.getter(#settings),
        returnValue: _FakeWebSettings_20(
          this,
          Invocation.getter(#settings),
        ),
        returnValueForMissingStub: _FakeWebSettings_20(
          this,
          Invocation.getter(#settings),
        ),
      ) as _i2.WebSettings);

  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i2.WebSettings pigeonVar_settings() => (super.noSuchMethod(
        Invocation.method(#pigeonVar_settings, []),
        returnValue: _FakeWebSettings_20(
          this,
          Invocation.method(#pigeonVar_settings, []),
        ),
        returnValueForMissingStub: _FakeWebSettings_20(
          this,
          Invocation.method(#pigeonVar_settings, []),
        ),
      ) as _i2.WebSettings);

  @override
  _i8.Future<void> loadData(String? data, String? mimeType, String? encoding) =>
      (super.noSuchMethod(
        Invocation.method(#loadData, [data, mimeType, encoding]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> loadDataWithBaseUrl(
    String? baseUrl,
    String? data,
    String? mimeType,
    String? encoding,
    String? historyUrl,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#loadDataWithBaseUrl, [
          baseUrl,
          data,
          mimeType,
          encoding,
          historyUrl,
        ]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> loadUrl(String? url, Map<String, String>? headers) =>
      (super.noSuchMethod(
        Invocation.method(#loadUrl, [url, headers]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> postUrl(String? url, _i13.Uint8List? data) =>
      (super.noSuchMethod(
        Invocation.method(#postUrl, [url, data]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<String?> getUrl() => (super.noSuchMethod(
        Invocation.method(#getUrl, []),
        returnValue: _i8.Future<String?>.value(),
        returnValueForMissingStub: _i8.Future<String?>.value(),
      ) as _i8.Future<String?>);

  @override
  _i8.Future<bool> canGoBack() => (super.noSuchMethod(
        Invocation.method(#canGoBack, []),
        returnValue: _i8.Future<bool>.value(false),
        returnValueForMissingStub: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> canGoForward() => (super.noSuchMethod(
        Invocation.method(#canGoForward, []),
        returnValue: _i8.Future<bool>.value(false),
        returnValueForMissingStub: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<void> goBack() => (super.noSuchMethod(
        Invocation.method(#goBack, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> goForward() => (super.noSuchMethod(
        Invocation.method(#goForward, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(#reload, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> clearCache(bool? includeDiskFiles) => (super.noSuchMethod(
        Invocation.method(#clearCache, [includeDiskFiles]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<String?> evaluateJavascript(String? javascriptString) =>
      (super.noSuchMethod(
        Invocation.method(#evaluateJavascript, [javascriptString]),
        returnValue: _i8.Future<String?>.value(),
        returnValueForMissingStub: _i8.Future<String?>.value(),
      ) as _i8.Future<String?>);

  @override
  _i8.Future<String?> getTitle() => (super.noSuchMethod(
        Invocation.method(#getTitle, []),
        returnValue: _i8.Future<String?>.value(),
        returnValueForMissingStub: _i8.Future<String?>.value(),
      ) as _i8.Future<String?>);

  @override
  _i8.Future<void> setWebViewClient(_i2.WebViewClient? client) =>
      (super.noSuchMethod(
        Invocation.method(#setWebViewClient, [client]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> addJavaScriptChannel(_i2.JavaScriptChannel? channel) =>
      (super.noSuchMethod(
        Invocation.method(#addJavaScriptChannel, [channel]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> removeJavaScriptChannel(String? name) => (super.noSuchMethod(
        Invocation.method(#removeJavaScriptChannel, [name]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setDownloadListener(_i2.DownloadListener? listener) =>
      (super.noSuchMethod(
        Invocation.method(#setDownloadListener, [listener]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setWebChromeClient(_i2.WebChromeClient? client) =>
      (super.noSuchMethod(
        Invocation.method(#setWebChromeClient, [client]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setBackgroundColor(int? color) => (super.noSuchMethod(
        Invocation.method(#setBackgroundColor, [color]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> destroy() => (super.noSuchMethod(
        Invocation.method(#destroy, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i2.WebView pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeWebView_7(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeWebView_7(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.WebView);

  @override
  _i8.Future<void> scrollTo(int? x, int? y) => (super.noSuchMethod(
        Invocation.method(#scrollTo, [x, y]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> scrollBy(int? x, int? y) => (super.noSuchMethod(
        Invocation.method(#scrollBy, [x, y]),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<_i2.WebViewPoint> getScrollPosition() => (super.noSuchMethod(
        Invocation.method(#getScrollPosition, []),
        returnValue: _i8.Future<_i2.WebViewPoint>.value(
          _FakeWebViewPoint_21(
            this,
            Invocation.method(#getScrollPosition, []),
          ),
        ),
        returnValueForMissingStub: _i8.Future<_i2.WebViewPoint>.value(
          _FakeWebViewPoint_21(
            this,
            Invocation.method(#getScrollPosition, []),
          ),
        ),
      ) as _i8.Future<_i2.WebViewPoint>);
}

/// A class which mocks [WebViewClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebViewClient extends _i1.Mock implements _i2.WebViewClient {
  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<void> setSynchronousReturnValueForShouldOverrideUrlLoading(
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSynchronousReturnValueForShouldOverrideUrlLoading,
          [value],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i2.WebViewClient pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeWebViewClient_1(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeWebViewClient_1(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.WebViewClient);
}

/// A class which mocks [WebStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebStorage extends _i1.Mock implements _i2.WebStorage {
  @override
  _i2.PigeonInstanceManager get pigeon_instanceManager => (super.noSuchMethod(
        Invocation.getter(#pigeon_instanceManager),
        returnValue: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
        returnValueForMissingStub: _FakePigeonInstanceManager_12(
          this,
          Invocation.getter(#pigeon_instanceManager),
        ),
      ) as _i2.PigeonInstanceManager);

  @override
  _i8.Future<void> deleteAllData() => (super.noSuchMethod(
        Invocation.method(#deleteAllData, []),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i2.WebStorage pigeon_copy() => (super.noSuchMethod(
        Invocation.method(#pigeon_copy, []),
        returnValue: _FakeWebStorage_11(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
        returnValueForMissingStub: _FakeWebStorage_11(
          this,
          Invocation.method(#pigeon_copy, []),
        ),
      ) as _i2.WebStorage);
}
