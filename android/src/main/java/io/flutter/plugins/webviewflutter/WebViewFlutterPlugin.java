// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.webviewflutter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.tencent.smtt.export.external.TbsCoreSettings;
import com.tencent.smtt.sdk.QbSdk;
import com.tencent.smtt.sdk.TbsDownloader;
import com.tencent.smtt.sdk.TbsListener;

import java.util.HashMap;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;

/**
 * Java platform implementation of the webview_flutter plugin.
 *
 * <p>Register this in an add to app scenario to gracefully handle activity and context changes.
 */
public class WebViewFlutterPlugin implements FlutterPlugin, ActivityAware {
    private static final String TAG = "WebViewFlutterPlugin";
    private FlutterPluginBinding pluginBinding;
    private ProxyApiRegistrar proxyApiRegistrar;

    @SuppressLint("StaticFieldLeak")
    public static Activity activity;

    public Context context;

    /**
     * Add an instance of this to {@link io.flutter.embedding.engine.plugins.PluginRegistry} to
     * register it.
     *
     * <p>Registration should eventually be handled automatically by v2 of the
     * GeneratedPluginRegistrant. https://github.com/flutter/flutter/issues/42694
     */
    public WebViewFlutterPlugin() {
        Log.e(TAG, "WebViewFlutterPlugin: 构造函数被调用");
    }

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding binding) {
        Log.e("LSL", "WebViewFlutterPlugin: onAttachedToEngine被调用");
        pluginBinding = binding;
        context = pluginBinding.getApplicationContext();
        initX5();
        setupProxyApiRegistrar(binding);
        Log.e("LSL", "WebViewFlutterPlugin: onAttachedToEngine完成");
    }

    private void setupProxyApiRegistrar(FlutterPluginBinding binding) {
        proxyApiRegistrar =
                new ProxyApiRegistrar(
                        binding.getBinaryMessenger(),
                        binding.getApplicationContext(),
                        new FlutterAssetManager.PluginBindingFlutterAssetManager(
                                binding.getApplicationContext().getAssets(), binding.getFlutterAssets()));

        Log.e("LSL", "setupProxyApiRegistrar: 开始注册PlatformViewFactory");
        FlutterViewFactory viewFactory = new FlutterViewFactory(proxyApiRegistrar.getInstanceManager());
        Log.e("LSL", "setupProxyApiRegistrar: 创建FlutterViewFactory成功: " + viewFactory);

        binding
                .getPlatformViewRegistry()
                .registerViewFactory(
                        "plugins.flutter.io/webview",
                        viewFactory);

        Log.e("LSL", "setupProxyApiRegistrar: PlatformViewFactory注册完成");
        proxyApiRegistrar.setUp();
        Log.e("LSL", "setupProxyApiRegistrar: proxyApiRegistrar.setUp()完成");
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        if (proxyApiRegistrar != null) {
            proxyApiRegistrar.tearDown();
            proxyApiRegistrar.getInstanceManager().stopFinalizationListener();
            proxyApiRegistrar = null;
        }
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding activityPluginBinding) {
        activity = activityPluginBinding.getActivity();
        if (proxyApiRegistrar != null) {
            proxyApiRegistrar.setContext(activityPluginBinding.getActivity());
        }
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        proxyApiRegistrar.setContext(pluginBinding.getApplicationContext());
    }

    @Override
    public void onReattachedToActivityForConfigChanges(
            @NonNull ActivityPluginBinding activityPluginBinding) {
        proxyApiRegistrar.setContext(activityPluginBinding.getActivity());
    }

    @Override
    public void onDetachedFromActivity() {
        proxyApiRegistrar.setContext(pluginBinding.getApplicationContext());
    }

    /**
     * Maintains instances used to communicate with the corresponding objects in Dart.
     */
    @Nullable
    public AndroidWebkitLibraryPigeonInstanceManager getInstanceManager() {
        return proxyApiRegistrar.getInstanceManager();
    }

    private void initX5() {
        Log.e("LSL", "initX5");
        HashMap<String, Object> settings = new HashMap<>();
        settings.put(TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER, true);
        settings.put(TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE, true);
        QbSdk.initTbsSettings(settings);
        QbSdk.setDownloadWithoutWifi(true);
        QbSdk.setTbsListener(new TbsListener() {
            @Override
            public void onDownloadFinish(int i) {
                Log.d(TAG, "onDownloadFinish: " + i);
            }

            @Override
            public void onInstallFinish(int i) {
                Log.d(TAG, "onInstallFinish: " + i);
            }

            @Override
            public void onDownloadProgress(int i) {
                Log.d(TAG, "onDownloadProgress: " + i);
            }
        });
        QbSdk.initX5Environment(context, new QbSdk.PreInitCallback() {
            @Override
            public void onCoreInitFinished() {
                Log.e(TAG, "onCoreInitFinished: ");
            }

            @Override
            public void onViewInitFinished(boolean isX5) {
                Log.e(TAG, "onViewInitFinished: " + isX5);
                if (!isX5) {
                    QbSdk.reset(context);
                    TbsDownloader.startDownload(context);
                }
            }
        });
    }
}
