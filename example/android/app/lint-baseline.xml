<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.5.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.5.1)" variant="all" version="8.5.1">

    <issue
        id="MissingClass"
        message="Class referenced in the manifest, `com.tencent.smtt.export.external.DexClassLoaderProviderService`, was not found in the project or the libraries"
        errorLine1="            android:name=&quot;com.tencent.smtt.export.external.DexClassLoaderProviderService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="28"
            column="27"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the `MediaStore.createWriteRequest` intent."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="47"
            column="36"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `usesCleartextTraffic` is only used in API level 23 and higher (current min is 21)"
        errorLine1="    &lt;application android:usesCleartextTraffic=&quot;true&quot;>"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/debug/AndroidManifest.xml"
            line="7"
            column="18"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `usesCleartextTraffic` is only used in API level 23 and higher (current min is 21)"
        errorLine1="        android:usesCleartextTraffic=&quot;true&quot;>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="ManifestOrder"
        message="`&lt;uses-permission>` tag appears after `&lt;application>` tag"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.INTERNET&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="37"
            column="6"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test:runner than 1.2.0 is available: 1.7.0"
        errorLine1="    androidTestImplementation &apos;androidx.test:runner:1.2.0&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="72"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.2.0 is available: 3.7.0"
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.2.0&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="73"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test:core than 1.4.0 is available: 1.7.0"
        errorLine1="    api &apos;androidx.test:core:1.4.0&apos;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="74"
            column="9"/>
    </issue>

</issues>
