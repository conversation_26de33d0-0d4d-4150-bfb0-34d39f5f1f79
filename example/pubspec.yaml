name: webview_flutter_android_example
description: Demonstrates how to use the webview_flutter_android plugin.
publish_to: none

environment:
  sdk: ^3.5.0
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  path_provider: ^2.0.6
  webview_flutter_android:
    # When depending on this package from a real application you should use:
    #   webview_flutter: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../
  webview_flutter_platform_interface: ^2.10.0

  url_launcher: ^6.3.1

dev_dependencies:
  espresso: ^0.4.0
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  assets:
    - assets/sample_audio.ogg
    - assets/sample_video.mp4
    - assets/www/index.html
    - assets/www/styles/style.css
