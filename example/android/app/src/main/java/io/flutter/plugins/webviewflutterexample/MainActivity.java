package io.flutter.plugins.webviewflutterexample;

import androidx.annotation.NonNull;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugins.GeneratedPluginRegistrant;
import android.util.Log;

public class MainActivity extends FlutterActivity {
    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        Log.e("LSL", "MainActivity: configureFlutterEngine被调用");
        super.configureFlutterEngine(flutterEngine);
        
        Log.e("LSL", "MainActivity: 开始手动注册插件");
        GeneratedPluginRegistrant.registerWith(flutterEngine);
        Log.e("LSL", "MainActivity: 插件注册完成");
    }
}
