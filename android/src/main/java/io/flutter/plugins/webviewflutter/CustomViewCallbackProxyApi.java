// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.webviewflutter;

import androidx.annotation.NonNull;

import com.tencent.smtt.export.external.interfaces.IX5WebChromeClient;

/**
 * Host API implementation for `CustomViewCallback`.
 *
 * <p>This class may handle instantiating and adding native object instances that are attached to a
 * Dart instance or handle method calls on the associated native class or an instance of the class.
 */
public class CustomViewCallbackProxyApi extends PigeonApiCustomViewCallback {
  /** Constructs a {@link CustomViewCallbackProxyApi}. */
  public CustomViewCallbackProxyApi(@NonNull ProxyApiRegistrar pigeonRegistrar) {
    super(pigeonRegistrar);
  }

  @Override
  public void onCustomViewHidden(@NonNull IX5WebChromeClient.CustomViewCallback pigeon_instance) {
    pigeon_instance.onCustomViewHidden();
  }
}
