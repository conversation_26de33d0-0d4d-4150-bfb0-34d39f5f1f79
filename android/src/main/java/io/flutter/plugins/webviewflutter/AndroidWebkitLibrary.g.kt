// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from <PERSON><PERSON> (v25.3.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon
@file:Suppress("UNCHECKED_CAST", "ArrayInDataClass")

package io.flutter.plugins.webviewflutter

import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMethodCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
private object AndroidWebkitLibraryPigeonUtils {

  fun createConnectionError(channelName: String): AndroidWebKitError {
    return AndroidWebKitError("channel-error",  "Unable to establish connection on channel: '$channelName'.", "")  }

  fun wrapResult(result: Any?): List<Any?> {
    return listOf(result)
  }

  fun wrapError(exception: Throwable): List<Any?> {
    return if (exception is AndroidWebKitError) {
      listOf(
        exception.code,
        exception.message,
        exception.details
      )
    } else {
      listOf(
        exception.javaClass.simpleName,
        exception.toString(),
        "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception)
      )
    }
  }
}

/**
 * Error class for passing custom error details to Flutter via a thrown PlatformException.
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class AndroidWebKitError (
  val code: String,
  override val message: String? = null,
  val details: Any? = null
) : Throwable()
/**
 * Maintains instances used to communicate with the corresponding objects in Dart.
 *
 * Objects stored in this container are represented by an object in Dart that is also stored in
 * an InstanceManager with the same identifier.
 *
 * When an instance is added with an identifier, either can be used to retrieve the other.
 *
 * Added instances are added as a weak reference and a strong reference. When the strong
 * reference is removed with [remove] and the weak reference is deallocated, the
 * `finalizationListener.onFinalize` is called with the instance's identifier. However, if the strong
 * reference is removed and then the identifier is retrieved with the intention to pass the identifier
 * to Dart (e.g. calling [getIdentifierForStrongReference]), the strong reference to the instance
 * is recreated. The strong reference will then need to be removed manually again.
 */
@Suppress("UNCHECKED_CAST", "MemberVisibilityCanBePrivate")
class AndroidWebkitLibraryPigeonInstanceManager(private val finalizationListener: PigeonFinalizationListener) {
  /** Interface for listening when a weak reference of an instance is removed from the manager.  */
  interface PigeonFinalizationListener {
    fun onFinalize(identifier: Long)
  }

  private val identifiers = java.util.WeakHashMap<Any, Long>()
  private val weakInstances = HashMap<Long, java.lang.ref.WeakReference<Any>>()
  private val strongInstances = HashMap<Long, Any>()
  private val referenceQueue = java.lang.ref.ReferenceQueue<Any>()
  private val weakReferencesToIdentifiers = HashMap<java.lang.ref.WeakReference<Any>, Long>()
  private val handler = android.os.Handler(android.os.Looper.getMainLooper())
  private val releaseAllFinalizedInstancesRunnable = Runnable {
    this.releaseAllFinalizedInstances()
  }
  private var nextIdentifier: Long = minHostCreatedIdentifier
  private var hasFinalizationListenerStopped = false

  /**
   * Modifies the time interval used to define how often this instance removes garbage collected
   * weak references to native Android objects that this instance was managing.
   */
  var clearFinalizedWeakReferencesInterval: Long = 3000
    set(value) {
      handler.removeCallbacks(releaseAllFinalizedInstancesRunnable)
      field = value
      releaseAllFinalizedInstances()
    }

  init {
    handler.postDelayed(releaseAllFinalizedInstancesRunnable, clearFinalizedWeakReferencesInterval)
  }

  companion object {
    // Identifiers are locked to a specific range to avoid collisions with objects
    // created simultaneously from Dart.
    // Host uses identifiers >= 2^16 and Dart is expected to use values n where,
    // 0 <= n < 2^16.
    private const val minHostCreatedIdentifier: Long = 65536
    private const val tag = "PigeonInstanceManager"

    /**
     * Instantiate a new manager with a listener for garbage collected weak
     * references.
     *
     * When the manager is no longer needed, [stopFinalizationListener] must be called.
     */
    fun create(finalizationListener: PigeonFinalizationListener): AndroidWebkitLibraryPigeonInstanceManager {
      return AndroidWebkitLibraryPigeonInstanceManager(finalizationListener)
    }
  }

  /**
   * Removes `identifier` and return its associated strongly referenced instance, if present,
   * from the manager.
   */
  fun <T> remove(identifier: Long): T? {
    logWarningIfFinalizationListenerHasStopped()
    return strongInstances.remove(identifier) as T?
  }

  /**
   * Retrieves the identifier paired with an instance, if present, otherwise `null`.
   *
   *
   * If the manager contains a strong reference to `instance`, it will return the identifier
   * associated with `instance`. If the manager contains only a weak reference to `instance`, a new
   * strong reference to `instance` will be added and will need to be removed again with [remove].
   *
   *
   * If this method returns a nonnull identifier, this method also expects the Dart
   * `AndroidWebkitLibraryPigeonInstanceManager` to have, or recreate, a weak reference to the Dart instance the
   * identifier is associated with.
   */
  fun getIdentifierForStrongReference(instance: Any?): Long? {
    logWarningIfFinalizationListenerHasStopped()
    val identifier = identifiers[instance]
    if (identifier != null) {
      strongInstances[identifier] = instance!!
    }
    return identifier
  }

  /**
   * Adds a new instance that was instantiated from Dart.
   *
   * The same instance can be added multiple times, but each identifier must be unique. This
   * allows two objects that are equivalent (e.g. the `equals` method returns true and their
   * hashcodes are equal) to both be added.
   *
   * [identifier] must be >= 0 and unique.
   */
  fun addDartCreatedInstance(instance: Any, identifier: Long) {
    logWarningIfFinalizationListenerHasStopped()
    addInstance(instance, identifier)
  }

  /**
   * Adds a new unique instance that was instantiated from the host platform.
   *
   * If the manager contains [instance], this returns the corresponding identifier. If the
   * manager does not contain [instance], this adds the instance and returns a unique
   * identifier for that [instance].
   */
  fun addHostCreatedInstance(instance: Any): Long {
    logWarningIfFinalizationListenerHasStopped()
    require(!containsInstance(instance)) { "Instance of ${instance.javaClass} has already been added." }
    val identifier = nextIdentifier++
    addInstance(instance, identifier)
    return identifier
  }

  /** Retrieves the instance associated with identifier, if present, otherwise `null`. */
  fun <T> getInstance(identifier: Long): T? {
    logWarningIfFinalizationListenerHasStopped()
    val instance = weakInstances[identifier] as java.lang.ref.WeakReference<T>?
    return instance?.get()
  }

  /** Returns whether this manager contains the given `instance`. */
  fun containsInstance(instance: Any?): Boolean {
    logWarningIfFinalizationListenerHasStopped()
    return identifiers.containsKey(instance)
  }

  /**
   * Stops the periodic run of the [PigeonFinalizationListener] for instances that have been garbage
   * collected.
   *
   * The InstanceManager can continue to be used, but the [PigeonFinalizationListener] will no
   * longer be called and methods will log a warning.
   */
  fun stopFinalizationListener() {
    handler.removeCallbacks(releaseAllFinalizedInstancesRunnable)
    hasFinalizationListenerStopped = true
  }

  /**
   * Removes all of the instances from this manager.
   *
   * The manager will be empty after this call returns.
   */
  fun clear() {
    identifiers.clear()
    weakInstances.clear()
    strongInstances.clear()
    weakReferencesToIdentifiers.clear()
  }

  /**
   * Whether the [PigeonFinalizationListener] is still being called for instances that are garbage
   * collected.
   *
   * See [stopFinalizationListener].
   */
  fun hasFinalizationListenerStopped(): Boolean {
    return hasFinalizationListenerStopped
  }

  private fun releaseAllFinalizedInstances() {
    if (hasFinalizationListenerStopped()) {
      return
    }
    var reference: java.lang.ref.WeakReference<Any>?
    while ((referenceQueue.poll() as java.lang.ref.WeakReference<Any>?).also { reference = it } != null) {
      val identifier = weakReferencesToIdentifiers.remove(reference)
      if (identifier != null) {
        weakInstances.remove(identifier)
        strongInstances.remove(identifier)
        finalizationListener.onFinalize(identifier)
      }
    }
    handler.postDelayed(releaseAllFinalizedInstancesRunnable, clearFinalizedWeakReferencesInterval)
  }

  private fun addInstance(instance: Any, identifier: Long) {
    require(identifier >= 0) { "Identifier must be >= 0: $identifier" }
    require(!weakInstances.containsKey(identifier)) {
      "Identifier has already been added: $identifier"
    }
    val weakReference = java.lang.ref.WeakReference(instance, referenceQueue)
    identifiers[instance] = identifier
    weakInstances[identifier] = weakReference
    weakReferencesToIdentifiers[weakReference] = identifier
    strongInstances[identifier] = instance
  }

  private fun logWarningIfFinalizationListenerHasStopped() {
    if (hasFinalizationListenerStopped()) {
      Log.w(
        tag,
        "The manager was used after calls to the PigeonFinalizationListener has been stopped."
      )
    }
  }
}


/** Generated API for managing the Dart and native `InstanceManager`s. */
private class AndroidWebkitLibraryPigeonInstanceManagerApi(val binaryMessenger: BinaryMessenger) {
  companion object {
    /** The codec used by AndroidWebkitLibraryPigeonInstanceManagerApi. */
    val codec: MessageCodec<Any?> by lazy {
      AndroidWebkitLibraryPigeonCodec()
    }

    /**
     * Sets up an instance of `AndroidWebkitLibraryPigeonInstanceManagerApi` to handle messages from the
     * `binaryMessenger`.
     */
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, instanceManager: AndroidWebkitLibraryPigeonInstanceManager?) {
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.removeStrongReference", codec)
        if (instanceManager != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              instanceManager.remove<Any?>(identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.clear", codec)
        if (instanceManager != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              instanceManager.clear()
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  fun removeStrongReference(identifierArg: Long, callback: (Result<Unit>) -> Unit)
{
    val channelName = "dev.flutter.pigeon.webview_flutter_android.PigeonInternalInstanceManager.removeStrongReference"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(identifierArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }
}
/**
 * Provides implementations for each ProxyApi implementation and provides access to resources
 * needed by any implementation.
 */
abstract class AndroidWebkitLibraryPigeonProxyApiRegistrar(val binaryMessenger: BinaryMessenger) {
  /** Whether APIs should ignore calling to Dart. */
  public var ignoreCallsToDart = false
  val instanceManager: AndroidWebkitLibraryPigeonInstanceManager
  private var _codec: MessageCodec<Any?>? = null
  val codec: MessageCodec<Any?>
    get() {
      if (_codec == null) {
        _codec = AndroidWebkitLibraryPigeonProxyApiBaseCodec(this)
      }
      return _codec!!
    }

  init {
    val api = AndroidWebkitLibraryPigeonInstanceManagerApi(binaryMessenger)
    instanceManager = AndroidWebkitLibraryPigeonInstanceManager.create(
      object : AndroidWebkitLibraryPigeonInstanceManager.PigeonFinalizationListener {
        override fun onFinalize(identifier: Long) {
          api.removeStrongReference(identifier) {
            if (it.isFailure) {
              Log.e(
                "PigeonProxyApiRegistrar",
                "Failed to remove Dart strong reference with identifier: $identifier"
              )
            }
          }
        }
      }
    )
  }
  /**
   * An implementation of [PigeonApiWebResourceRequest] used to add a new Dart instance of
   * `WebResourceRequest` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebResourceRequest(): PigeonApiWebResourceRequest

  /**
   * An implementation of [PigeonApiWebResourceResponse] used to add a new Dart instance of
   * `WebResourceResponse` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebResourceResponse(): PigeonApiWebResourceResponse

  /**
   * An implementation of [PigeonApiWebResourceError] used to add a new Dart instance of
   * `WebResourceError` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebResourceError(): PigeonApiWebResourceError

  /**
   * An implementation of [PigeonApiWebResourceErrorCompat] used to add a new Dart instance of
   * `WebResourceErrorCompat` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebResourceErrorCompat(): PigeonApiWebResourceErrorCompat

  /**
   * An implementation of [PigeonApiWebViewPoint] used to add a new Dart instance of
   * `WebViewPoint` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebViewPoint(): PigeonApiWebViewPoint

  /**
   * An implementation of [PigeonApiConsoleMessage] used to add a new Dart instance of
   * `ConsoleMessage` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiConsoleMessage(): PigeonApiConsoleMessage

  /**
   * An implementation of [PigeonApiCookieManager] used to add a new Dart instance of
   * `CookieManager` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiCookieManager(): PigeonApiCookieManager

  /**
   * An implementation of [PigeonApiWebView] used to add a new Dart instance of
   * `WebView` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebView(): PigeonApiWebView

  /**
   * An implementation of [PigeonApiWebSettings] used to add a new Dart instance of
   * `WebSettings` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebSettings(): PigeonApiWebSettings

  /**
   * An implementation of [PigeonApiJavaScriptChannel] used to add a new Dart instance of
   * `JavaScriptChannel` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiJavaScriptChannel(): PigeonApiJavaScriptChannel

  /**
   * An implementation of [PigeonApiWebViewClient] used to add a new Dart instance of
   * `WebViewClient` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebViewClient(): PigeonApiWebViewClient

  /**
   * An implementation of [PigeonApiDownloadListener] used to add a new Dart instance of
   * `DownloadListener` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiDownloadListener(): PigeonApiDownloadListener

  /**
   * An implementation of [PigeonApiWebChromeClient] used to add a new Dart instance of
   * `WebChromeClient` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebChromeClient(): PigeonApiWebChromeClient

  /**
   * An implementation of [PigeonApiFlutterAssetManager] used to add a new Dart instance of
   * `FlutterAssetManager` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiFlutterAssetManager(): PigeonApiFlutterAssetManager

  /**
   * An implementation of [PigeonApiWebStorage] used to add a new Dart instance of
   * `WebStorage` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiWebStorage(): PigeonApiWebStorage

  /**
   * An implementation of [PigeonApiFileChooserParams] used to add a new Dart instance of
   * `FileChooserParams` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiFileChooserParams(): PigeonApiFileChooserParams

  /**
   * An implementation of [PigeonApiPermissionRequest] used to add a new Dart instance of
   * `PermissionRequest` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiPermissionRequest(): PigeonApiPermissionRequest

  /**
   * An implementation of [PigeonApiCustomViewCallback] used to add a new Dart instance of
   * `CustomViewCallback` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiCustomViewCallback(): PigeonApiCustomViewCallback

  /**
   * An implementation of [PigeonApiView] used to add a new Dart instance of
   * `View` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiView(): PigeonApiView

  /**
   * An implementation of [PigeonApiGeolocationPermissionsCallback] used to add a new Dart instance of
   * `GeolocationPermissionsCallback` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiGeolocationPermissionsCallback(): PigeonApiGeolocationPermissionsCallback

  /**
   * An implementation of [PigeonApiHttpAuthHandler] used to add a new Dart instance of
   * `HttpAuthHandler` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiHttpAuthHandler(): PigeonApiHttpAuthHandler

  /**
   * An implementation of [PigeonApiAndroidMessage] used to add a new Dart instance of
   * `AndroidMessage` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiAndroidMessage(): PigeonApiAndroidMessage

  /**
   * An implementation of [PigeonApiClientCertRequest] used to add a new Dart instance of
   * `ClientCertRequest` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiClientCertRequest(): PigeonApiClientCertRequest

  /**
   * An implementation of [PigeonApiPrivateKey] used to add a new Dart instance of
   * `PrivateKey` to the Dart `InstanceManager`.
   */
  open fun getPigeonApiPrivateKey(): PigeonApiPrivateKey
  {
    return PigeonApiPrivateKey(this)
  }

  /**
   * An implementation of [PigeonApiX509Certificate] used to add a new Dart instance of
   * `X509Certificate` to the Dart `InstanceManager`.
   */
  open fun getPigeonApiX509Certificate(): PigeonApiX509Certificate
  {
    return PigeonApiX509Certificate(this)
  }

  /**
   * An implementation of [PigeonApiSslErrorHandler] used to add a new Dart instance of
   * `SslErrorHandler` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiSslErrorHandler(): PigeonApiSslErrorHandler

  /**
   * An implementation of [PigeonApiSslError] used to add a new Dart instance of
   * `SslError` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiSslError(): PigeonApiSslError

  /**
   * An implementation of [PigeonApiSslCertificateDName] used to add a new Dart instance of
   * `SslCertificateDName` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiSslCertificateDName(): PigeonApiSslCertificateDName

  /**
   * An implementation of [PigeonApiSslCertificate] used to add a new Dart instance of
   * `SslCertificate` to the Dart `InstanceManager`.
   */
  abstract fun getPigeonApiSslCertificate(): PigeonApiSslCertificate

  fun setUp() {
    AndroidWebkitLibraryPigeonInstanceManagerApi.setUpMessageHandlers(binaryMessenger, instanceManager)
    PigeonApiCookieManager.setUpMessageHandlers(binaryMessenger, getPigeonApiCookieManager())
    PigeonApiWebView.setUpMessageHandlers(binaryMessenger, getPigeonApiWebView())
    PigeonApiWebSettings.setUpMessageHandlers(binaryMessenger, getPigeonApiWebSettings())
    PigeonApiJavaScriptChannel.setUpMessageHandlers(binaryMessenger, getPigeonApiJavaScriptChannel())
    PigeonApiWebViewClient.setUpMessageHandlers(binaryMessenger, getPigeonApiWebViewClient())
    PigeonApiDownloadListener.setUpMessageHandlers(binaryMessenger, getPigeonApiDownloadListener())
    PigeonApiWebChromeClient.setUpMessageHandlers(binaryMessenger, getPigeonApiWebChromeClient())
    PigeonApiFlutterAssetManager.setUpMessageHandlers(binaryMessenger, getPigeonApiFlutterAssetManager())
    PigeonApiWebStorage.setUpMessageHandlers(binaryMessenger, getPigeonApiWebStorage())
    PigeonApiPermissionRequest.setUpMessageHandlers(binaryMessenger, getPigeonApiPermissionRequest())
    PigeonApiCustomViewCallback.setUpMessageHandlers(binaryMessenger, getPigeonApiCustomViewCallback())
    PigeonApiView.setUpMessageHandlers(binaryMessenger, getPigeonApiView())
    PigeonApiGeolocationPermissionsCallback.setUpMessageHandlers(binaryMessenger, getPigeonApiGeolocationPermissionsCallback())
    PigeonApiHttpAuthHandler.setUpMessageHandlers(binaryMessenger, getPigeonApiHttpAuthHandler())
    PigeonApiAndroidMessage.setUpMessageHandlers(binaryMessenger, getPigeonApiAndroidMessage())
    PigeonApiClientCertRequest.setUpMessageHandlers(binaryMessenger, getPigeonApiClientCertRequest())
    PigeonApiSslErrorHandler.setUpMessageHandlers(binaryMessenger, getPigeonApiSslErrorHandler())
    PigeonApiSslError.setUpMessageHandlers(binaryMessenger, getPigeonApiSslError())
    PigeonApiSslCertificateDName.setUpMessageHandlers(binaryMessenger, getPigeonApiSslCertificateDName())
    PigeonApiSslCertificate.setUpMessageHandlers(binaryMessenger, getPigeonApiSslCertificate())
  }
  fun tearDown() {
    AndroidWebkitLibraryPigeonInstanceManagerApi.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiCookieManager.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiWebView.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiWebSettings.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiJavaScriptChannel.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiWebViewClient.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiDownloadListener.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiWebChromeClient.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiFlutterAssetManager.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiWebStorage.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiPermissionRequest.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiCustomViewCallback.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiView.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiGeolocationPermissionsCallback.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiHttpAuthHandler.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiAndroidMessage.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiClientCertRequest.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiSslErrorHandler.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiSslError.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiSslCertificateDName.setUpMessageHandlers(binaryMessenger, null)
    PigeonApiSslCertificate.setUpMessageHandlers(binaryMessenger, null)
  }
}
private class AndroidWebkitLibraryPigeonProxyApiBaseCodec(val registrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) : AndroidWebkitLibraryPigeonCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      128.toByte() -> {
        val identifier: Long = readValue(buffer) as Long
        val instance: Any? = registrar.instanceManager.getInstance(identifier)
        if (instance == null) {
          Log.e(
            "PigeonProxyApiBaseCodec",
            "Failed to find instance with identifier: $identifier"
          )
        }
        return instance
      }
      else -> super.readValueOfType(type, buffer)
    }
  }

  override fun writeValue(stream: ByteArrayOutputStream, value: Any?) {
    if (value is Boolean || value is ByteArray || value is Double || value is DoubleArray || value is FloatArray || value is Int || value is IntArray || value is List<*> || value is Long || value is LongArray || value is Map<*, *> || value is String || value is FileChooserMode || value is ConsoleMessageLevel || value is SslErrorType || value == null) {
      super.writeValue(stream, value)
      return
    }

    if (value is com.tencent.smtt.export.external.interfaces.WebResourceRequest) {
      registrar.getPigeonApiWebResourceRequest().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.WebResourceResponse) {
      registrar.getPigeonApiWebResourceResponse().pigeon_newInstance(value) { }
    }
     else if (android.os.Build.VERSION.SDK_INT >= 23 && value is com.tencent.smtt.export.external.interfaces.WebResourceError) {
      registrar.getPigeonApiWebResourceError().pigeon_newInstance(value) { }
    }
     else if (value is androidx.webkit.WebResourceErrorCompat) {
      registrar.getPigeonApiWebResourceErrorCompat().pigeon_newInstance(value) { }
    }
     else if (value is WebViewPoint) {
      registrar.getPigeonApiWebViewPoint().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.ConsoleMessage) {
      registrar.getPigeonApiConsoleMessage().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.CookieManager) {
      registrar.getPigeonApiCookieManager().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.WebView) {
      registrar.getPigeonApiWebView().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.WebSettings) {
      registrar.getPigeonApiWebSettings().pigeon_newInstance(value) { }
    }
     else if (value is JavaScriptChannel) {
      registrar.getPigeonApiJavaScriptChannel().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.WebViewClient) {
      registrar.getPigeonApiWebViewClient().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.DownloadListener) {
      registrar.getPigeonApiDownloadListener().pigeon_newInstance(value) { }
    }
     else if (value is io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl) {
      registrar.getPigeonApiWebChromeClient().pigeon_newInstance(value) { }
    }
     else if (value is io.flutter.plugins.webviewflutter.FlutterAssetManager) {
      registrar.getPigeonApiFlutterAssetManager().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.WebStorage) {
      registrar.getPigeonApiWebStorage().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.sdk.WebChromeClient.FileChooserParams) {
      registrar.getPigeonApiFileChooserParams().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.PermissionRequest) {
      registrar.getPigeonApiPermissionRequest().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.IX5WebChromeClient.CustomViewCallback) {
      registrar.getPigeonApiCustomViewCallback().pigeon_newInstance(value) { }
    }
     else if (value is android.view.View) {
      registrar.getPigeonApiView().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback) {
      registrar.getPigeonApiGeolocationPermissionsCallback().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.HttpAuthHandler) {
      registrar.getPigeonApiHttpAuthHandler().pigeon_newInstance(value) { }
    }
     else if (value is android.os.Message) {
      registrar.getPigeonApiAndroidMessage().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.ClientCertRequest) {
      registrar.getPigeonApiClientCertRequest().pigeon_newInstance(value) { }
    }
     else if (value is java.security.PrivateKey) {
      registrar.getPigeonApiPrivateKey().pigeon_newInstance(value) { }
    }
     else if (value is java.security.cert.X509Certificate) {
      registrar.getPigeonApiX509Certificate().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.SslErrorHandler) {
      registrar.getPigeonApiSslErrorHandler().pigeon_newInstance(value) { }
    }
     else if (value is com.tencent.smtt.export.external.interfaces.SslError) {
      registrar.getPigeonApiSslError().pigeon_newInstance(value) { }
    }
     else if (value is android.net.http.SslCertificate.DName) {
      registrar.getPigeonApiSslCertificateDName().pigeon_newInstance(value) { }
    }
     else if (value is android.net.http.SslCertificate) {
      registrar.getPigeonApiSslCertificate().pigeon_newInstance(value) { }
    }

    when {
      registrar.instanceManager.containsInstance(value) -> {
        stream.write(128)
        writeValue(stream, registrar.instanceManager.getIdentifierForStrongReference(value))
      }
      else -> throw IllegalArgumentException("Unsupported value: '$value' of type '${value.javaClass.name}'")
    }
  }
}

/**
 * Mode of how to select files for a file chooser.
 *
 * See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams.
 */
enum class FileChooserMode(val raw: Int) {
  /**
   * Open single file and requires that the file exists before allowing the
   * user to pick it.
   *
   * See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_OPEN.
   */
  OPEN(0),
  /**
   * Similar to [open] but allows multiple files to be selected.
   *
   * See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_OPEN_MULTIPLE.
   */
  OPEN_MULTIPLE(1),
  /**
   * Allows picking a nonexistent file and saving it.
   *
   * See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams#MODE_SAVE.
   */
  SAVE(2),
  /**
   * Indicates a `FileChooserMode` with an unknown mode.
   *
   * This does not represent an actual value provided by the platform and only
   * indicates a value was provided that isn't currently supported.
   */
  UNKNOWN(3);

  companion object {
    fun ofRaw(raw: Int): FileChooserMode? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * Indicates the type of message logged to the console.
 *
 * See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel.
 */
enum class ConsoleMessageLevel(val raw: Int) {
  /**
   * Indicates a message is logged for debugging.
   *
   * See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#DEBUG.
   */
  DEBUG(0),
  /**
   * Indicates a message is provided as an error.
   *
   * See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#ERROR.
   */
  ERROR(1),
  /**
   * Indicates a message is provided as a basic log message.
   *
   * See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#LOG.
   */
  LOG(2),
  /**
   * Indicates a message is provided as a tip.
   *
   * See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#TIP.
   */
  TIP(3),
  /**
   * Indicates a message is provided as a warning.
   *
   * See https://developer.android.com/reference/android/webkit/ConsoleMessage.MessageLevel#WARNING.
   */
  WARNING(4),
  /**
   * Indicates a message with an unknown level.
   *
   * This does not represent an actual value provided by the platform and only
   * indicates a value was provided that isn't currently supported.
   */
  UNKNOWN(5);

  companion object {
    fun ofRaw(raw: Int): ConsoleMessageLevel? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * Type of error for a SslCertificate.
 *
 * See https://developer.android.com/reference/android/net/http/SslError#SSL_DATE_INVALID.
 */
enum class SslErrorType(val raw: Int) {
  /** The date of the certificate is invalid. */
  DATE_INVALID(0),
  /** The certificate has expired. */
  EXPIRED(1),
  /** Hostname mismatch. */
  ID_MISMATCH(2),
  /** A generic error occurred. */
  INVALID(3),
  /** The certificate is not yet valid. */
  NOT_YET_VALID(4),
  /** The certificate authority is not trusted. */
  UNTRUSTED(5),
  /** The type is not recognized by this wrapper. */
  UNKNOWN(6);

  companion object {
    fun ofRaw(raw: Int): SslErrorType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}
private open class AndroidWebkitLibraryPigeonCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      129.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          FileChooserMode.ofRaw(it.toInt())
        }
      }
      130.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          ConsoleMessageLevel.ofRaw(it.toInt())
        }
      }
      131.toByte() -> {
        return (readValue(buffer) as Long?)?.let {
          SslErrorType.ofRaw(it.toInt())
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is FileChooserMode -> {
        stream.write(129)
        writeValue(stream, value.raw)
      }
      is ConsoleMessageLevel -> {
        stream.write(130)
        writeValue(stream, value.raw)
      }
      is SslErrorType -> {
        stream.write(131)
        writeValue(stream, value.raw)
      }
      else -> super.writeValue(stream, value)
    }
  }
}

/**
 * Encompasses parameters to the `WebViewClient.shouldInterceptRequest` method.
 *
 * See https://developer.android.com/reference/android/webkit/WebResourceRequest.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebResourceRequest(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** The URL for which the resource request was made. */
  abstract fun url(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceRequest): String

  /** Whether the request was made in order to fetch the main frame's document. */
  abstract fun isForMainFrame(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceRequest): Boolean

  /** Whether the request was a result of a server-side redirect. */
  abstract fun isRedirect(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceRequest): Boolean?

  /** Whether a gesture (such as a click) was associated with the request. */
  abstract fun hasGesture(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceRequest): Boolean

  /** The method associated with the request, for example "GET". */
  abstract fun method(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceRequest): String

  /** The headers associated with the request. */
  abstract fun requestHeaders(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceRequest): Map<String, String>?

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebResourceRequest and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.WebResourceRequest, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val urlArg = url(pigeon_instanceArg)
      val isForMainFrameArg = isForMainFrame(pigeon_instanceArg)
      val isRedirectArg = isRedirect(pigeon_instanceArg)
      val hasGestureArg = hasGesture(pigeon_instanceArg)
      val methodArg = method(pigeon_instanceArg)
      val requestHeadersArg = requestHeaders(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebResourceRequest.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, urlArg, isForMainFrameArg, isRedirectArg, hasGestureArg, methodArg, requestHeadersArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Encapsulates a resource response.
 *
 * See https://developer.android.com/reference/android/webkit/WebResourceResponse.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebResourceResponse(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** The resource response's status code. */
  abstract fun statusCode(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceResponse): Long

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebResourceResponse and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.WebResourceResponse, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val statusCodeArg = statusCode(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebResourceResponse.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, statusCodeArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Encapsulates information about errors that occurred during loading of web
 * resources.
 *
 * See https://developer.android.com/reference/android/webkit/WebResourceError.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebResourceError(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** The error code of the error. */
  @androidx.annotation.RequiresApi(api = 23)
  abstract fun errorCode(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceError): Long

  /** The string describing the error. */
  @androidx.annotation.RequiresApi(api = 23)
  abstract fun description(pigeon_instance: com.tencent.smtt.export.external.interfaces.WebResourceError): String

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebResourceError and attaches it to [pigeon_instanceArg]. */
  @androidx.annotation.RequiresApi(api = 23)
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.WebResourceError, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val errorCodeArg = errorCode(pigeon_instanceArg)
      val descriptionArg = description(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebResourceError.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, errorCodeArg, descriptionArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Encapsulates information about errors that occurred during loading of web
 * resources.
 *
 * See https://developer.android.com/reference/androidx/webkit/WebResourceErrorCompat.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebResourceErrorCompat(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** The error code of the error. */
  abstract fun errorCode(pigeon_instance: androidx.webkit.WebResourceErrorCompat): Long

  /** The string describing the error. */
  abstract fun description(pigeon_instance: androidx.webkit.WebResourceErrorCompat): String

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebResourceErrorCompat and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: androidx.webkit.WebResourceErrorCompat, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val errorCodeArg = errorCode(pigeon_instanceArg)
      val descriptionArg = description(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebResourceErrorCompat.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, errorCodeArg, descriptionArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Represents a position on a web page.
 *
 * This is a custom class created for convenience of the wrapper.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebViewPoint(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun x(pigeon_instance: WebViewPoint): Long

  abstract fun y(pigeon_instance: WebViewPoint): Long

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebViewPoint and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: WebViewPoint, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val xArg = x(pigeon_instanceArg)
      val yArg = y(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewPoint.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, xArg, yArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Represents a JavaScript console message from WebCore.
 *
 * See https://developer.android.com/reference/android/webkit/ConsoleMessage
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiConsoleMessage(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun lineNumber(pigeon_instance: com.tencent.smtt.export.external.interfaces.ConsoleMessage): Long

  abstract fun message(pigeon_instance: com.tencent.smtt.export.external.interfaces.ConsoleMessage): String

  abstract fun level(pigeon_instance: com.tencent.smtt.export.external.interfaces.ConsoleMessage): ConsoleMessageLevel

  abstract fun sourceId(pigeon_instance: com.tencent.smtt.export.external.interfaces.ConsoleMessage): String

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of ConsoleMessage and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.ConsoleMessage, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val lineNumberArg = lineNumber(pigeon_instanceArg)
      val messageArg = message(pigeon_instanceArg)
      val levelArg = level(pigeon_instanceArg)
      val sourceIdArg = sourceId(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.ConsoleMessage.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, lineNumberArg, messageArg, levelArg, sourceIdArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Manages the cookies used by an application's `WebView` instances.
 *
 * See https://developer.android.com/reference/android/webkit/CookieManager.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiCookieManager(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun instance(): com.tencent.smtt.sdk.CookieManager

  /** Sets a single cookie (key-value pair) for the given URL. */
  abstract fun setCookie(pigeon_instance: com.tencent.smtt.sdk.CookieManager, url: String, value: String)

  /** Removes all cookies. */
  abstract fun removeAllCookies(pigeon_instance: com.tencent.smtt.sdk.CookieManager, callback: (Result<Boolean>) -> Unit)

  /** Sets whether the `WebView` should allow third party cookies to be set. */
  abstract fun setAcceptThirdPartyCookies(pigeon_instance: com.tencent.smtt.sdk.CookieManager, webView: com.tencent.smtt.sdk.WebView, accept: Boolean)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiCookieManager?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.CookieManager.instance", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.instance(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.CookieManager.setCookie", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.CookieManager
            val urlArg = args[1] as String
            val valueArg = args[2] as String
            val wrapped: List<Any?> = try {
              api.setCookie(pigeon_instanceArg, urlArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.CookieManager.removeAllCookies", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.CookieManager
            api.removeAllCookies(pigeon_instanceArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(AndroidWebkitLibraryPigeonUtils.wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(AndroidWebkitLibraryPigeonUtils.wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.CookieManager.setAcceptThirdPartyCookies", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.CookieManager
            val webViewArg = args[1] as com.tencent.smtt.sdk.WebView
            val acceptArg = args[2] as Boolean
            val wrapped: List<Any?> = try {
              api.setAcceptThirdPartyCookies(pigeon_instanceArg, webViewArg, acceptArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of CookieManager and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.CookieManager, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.CookieManager.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * A View that displays web pages.
 *
 * See https://developer.android.com/reference/android/webkit/WebView.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebView(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun pigeon_defaultConstructor(): com.tencent.smtt.sdk.WebView

  /** The WebSettings object used to control the settings for this WebView. */
  abstract fun settings(pigeon_instance: com.tencent.smtt.sdk.WebView): com.tencent.smtt.sdk.WebSettings

  /** Loads the given data into this WebView using a 'data' scheme URL. */
  abstract fun loadData(pigeon_instance: com.tencent.smtt.sdk.WebView, data: String, mimeType: String?, encoding: String?)

  /**
   * Loads the given data into this WebView, using baseUrl as the base URL for
   * the content.
   */
  abstract fun loadDataWithBaseUrl(pigeon_instance: com.tencent.smtt.sdk.WebView, baseUrl: String?, data: String, mimeType: String?, encoding: String?, historyUrl: String?)

  /** Loads the given URL. */
  abstract fun loadUrl(pigeon_instance: com.tencent.smtt.sdk.WebView, url: String, headers: Map<String, String>)

  /** Loads the URL with postData using "POST" method into this WebView. */
  abstract fun postUrl(pigeon_instance: com.tencent.smtt.sdk.WebView, url: String, data: ByteArray)

  /** Gets the URL for the current page. */
  abstract fun getUrl(pigeon_instance: com.tencent.smtt.sdk.WebView): String?

  /** Gets whether this WebView has a back history item. */
  abstract fun canGoBack(pigeon_instance: com.tencent.smtt.sdk.WebView): Boolean

  /** Gets whether this WebView has a forward history item. */
  abstract fun canGoForward(pigeon_instance: com.tencent.smtt.sdk.WebView): Boolean

  /** Goes back in the history of this WebView. */
  abstract fun goBack(pigeon_instance: com.tencent.smtt.sdk.WebView)

  /** Goes forward in the history of this WebView. */
  abstract fun goForward(pigeon_instance: com.tencent.smtt.sdk.WebView)

  /** Reloads the current URL. */
  abstract fun reload(pigeon_instance: com.tencent.smtt.sdk.WebView)

  /** Clears the resource cache. */
  abstract fun clearCache(pigeon_instance: com.tencent.smtt.sdk.WebView, includeDiskFiles: Boolean)

  /**
   * Asynchronously evaluates JavaScript in the context of the currently
   * displayed page.
   */
  abstract fun evaluateJavascript(pigeon_instance: com.tencent.smtt.sdk.WebView, javascriptString: String, callback: (Result<String?>) -> Unit)

  /** Gets the title for the current page. */
  abstract fun getTitle(pigeon_instance: com.tencent.smtt.sdk.WebView): String?

  /**
   * Enables debugging of web contents (HTML / CSS / JavaScript) loaded into
   * any WebViews of this application.
   */
  abstract fun setWebContentsDebuggingEnabled(enabled: Boolean)

  /**
   * Sets the WebViewClient that will receive various notifications and
   * requests.
   */
  abstract fun setWebViewClient(pigeon_instance: com.tencent.smtt.sdk.WebView, client: com.tencent.smtt.sdk.WebViewClient?)

  /** Injects the supplied Java object into this WebView. */
  abstract fun addJavaScriptChannel(pigeon_instance: com.tencent.smtt.sdk.WebView, channel: JavaScriptChannel)

  /** Removes a previously injected Java object from this WebView. */
  abstract fun removeJavaScriptChannel(pigeon_instance: com.tencent.smtt.sdk.WebView, name: String)

  /**
   * Registers the interface to be used when content can not be handled by the
   * rendering engine, and should be downloaded instead.
   */
  abstract fun setDownloadListener(pigeon_instance: com.tencent.smtt.sdk.WebView, listener: com.tencent.smtt.sdk.DownloadListener?)

  /** Sets the chrome handler. */
  abstract fun setWebChromeClient(pigeon_instance: com.tencent.smtt.sdk.WebView, client: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl?)

  /** Sets the background color for this view. */
  abstract fun setBackgroundColor(pigeon_instance: com.tencent.smtt.sdk.WebView, color: Long)

  /** Destroys the internal state of this WebView. */
  abstract fun destroy(pigeon_instance: com.tencent.smtt.sdk.WebView)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiWebView?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.pigeon_defaultConstructor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.pigeon_defaultConstructor(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.settings", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val pigeon_identifierArg = args[1] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.settings(pigeon_instanceArg), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.loadData", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val dataArg = args[1] as String
            val mimeTypeArg = args[2] as String?
            val encodingArg = args[3] as String?
            val wrapped: List<Any?> = try {
              api.loadData(pigeon_instanceArg, dataArg, mimeTypeArg, encodingArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.loadDataWithBaseUrl", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val baseUrlArg = args[1] as String?
            val dataArg = args[2] as String
            val mimeTypeArg = args[3] as String?
            val encodingArg = args[4] as String?
            val historyUrlArg = args[5] as String?
            val wrapped: List<Any?> = try {
              api.loadDataWithBaseUrl(pigeon_instanceArg, baseUrlArg, dataArg, mimeTypeArg, encodingArg, historyUrlArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.loadUrl", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val urlArg = args[1] as String
            val headersArg = args[2] as Map<String, String>
            val wrapped: List<Any?> = try {
              api.loadUrl(pigeon_instanceArg, urlArg, headersArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.postUrl", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val urlArg = args[1] as String
            val dataArg = args[2] as ByteArray
            val wrapped: List<Any?> = try {
              api.postUrl(pigeon_instanceArg, urlArg, dataArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.getUrl", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              listOf(api.getUrl(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.canGoBack", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              listOf(api.canGoBack(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.canGoForward", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              listOf(api.canGoForward(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.goBack", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              api.goBack(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.goForward", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              api.goForward(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.reload", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              api.reload(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.clearCache", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val includeDiskFilesArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.clearCache(pigeon_instanceArg, includeDiskFilesArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.evaluateJavascript", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val javascriptStringArg = args[1] as String
            api.evaluateJavascript(pigeon_instanceArg, javascriptStringArg) { result: Result<String?> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(AndroidWebkitLibraryPigeonUtils.wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(AndroidWebkitLibraryPigeonUtils.wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.getTitle", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              listOf(api.getTitle(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.setWebContentsDebuggingEnabled", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val enabledArg = args[0] as Boolean
            val wrapped: List<Any?> = try {
              api.setWebContentsDebuggingEnabled(enabledArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.setWebViewClient", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val clientArg = args[1] as com.tencent.smtt.sdk.WebViewClient?
            val wrapped: List<Any?> = try {
              api.setWebViewClient(pigeon_instanceArg, clientArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.addJavaScriptChannel", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val channelArg = args[1] as JavaScriptChannel
            val wrapped: List<Any?> = try {
              api.addJavaScriptChannel(pigeon_instanceArg, channelArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.removeJavaScriptChannel", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val nameArg = args[1] as String
            val wrapped: List<Any?> = try {
              api.removeJavaScriptChannel(pigeon_instanceArg, nameArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.setDownloadListener", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val listenerArg = args[1] as com.tencent.smtt.sdk.DownloadListener?
            val wrapped: List<Any?> = try {
              api.setDownloadListener(pigeon_instanceArg, listenerArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.setWebChromeClient", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val clientArg = args[1] as io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl?
            val wrapped: List<Any?> = try {
              api.setWebChromeClient(pigeon_instanceArg, clientArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.setBackgroundColor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val colorArg = args[1] as Long
            val wrapped: List<Any?> = try {
              api.setBackgroundColor(pigeon_instanceArg, colorArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebView.destroy", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebView
            val wrapped: List<Any?> = try {
              api.destroy(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebView and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.WebView, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebView.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

  /**
   * This is called in response to an internal scroll in this view (i.e., the
   * view scrolled its own contents).
   */
  fun onScrollChanged(pigeon_instanceArg: com.tencent.smtt.sdk.WebView, leftArg: Long, topArg: Long, oldLeftArg: Long, oldTopArg: Long, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebView.onScrollChanged"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, leftArg, topArg, oldLeftArg, oldTopArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  @Suppress("FunctionName")
  /** An implementation of [PigeonApiView] used to access callback methods */
  fun pigeon_getPigeonApiView(): PigeonApiView
  {
    return pigeonRegistrar.getPigeonApiView()
  }

}
/**
 * Manages settings state for a `WebView`.
 *
 * See https://developer.android.com/reference/android/webkit/WebSettings.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebSettings(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Sets whether the DOM storage API is enabled. */
  abstract fun setDomStorageEnabled(pigeon_instance: com.tencent.smtt.sdk.WebSettings, flag: Boolean)

  /** Tells JavaScript to open windows automatically. */
  abstract fun setJavaScriptCanOpenWindowsAutomatically(pigeon_instance: com.tencent.smtt.sdk.WebSettings, flag: Boolean)

  /** Sets whether the WebView whether supports multiple windows. */
  abstract fun setSupportMultipleWindows(pigeon_instance: com.tencent.smtt.sdk.WebSettings, support: Boolean)

  /** Tells the WebView to enable JavaScript execution. */
  abstract fun setJavaScriptEnabled(pigeon_instance: com.tencent.smtt.sdk.WebSettings, flag: Boolean)

  /** Sets the WebView's user-agent string. */
  abstract fun setUserAgentString(pigeon_instance: com.tencent.smtt.sdk.WebSettings, userAgentString: String?)

  /** Sets whether the WebView requires a user gesture to play media. */
  abstract fun setMediaPlaybackRequiresUserGesture(pigeon_instance: com.tencent.smtt.sdk.WebSettings, require: Boolean)

  /**
   * Sets whether the WebView should support zooming using its on-screen zoom
   * controls and gestures.
   */
  abstract fun setSupportZoom(pigeon_instance: com.tencent.smtt.sdk.WebSettings, support: Boolean)

  /**
   * Sets whether the WebView loads pages in overview mode, that is, zooms out
   * the content to fit on screen by width.
   */
  abstract fun setLoadWithOverviewMode(pigeon_instance: com.tencent.smtt.sdk.WebSettings, overview: Boolean)

  /**
   * Sets whether the WebView should enable support for the "viewport" HTML
   * meta tag or should use a wide viewport.
   */
  abstract fun setUseWideViewPort(pigeon_instance: com.tencent.smtt.sdk.WebSettings, use: Boolean)

  /**
   * Sets whether the WebView should display on-screen zoom controls when using
   * the built-in zoom mechanisms.
   */
  abstract fun setDisplayZoomControls(pigeon_instance: com.tencent.smtt.sdk.WebSettings, enabled: Boolean)

  /**
   * Sets whether the WebView should display on-screen zoom controls when using
   * the built-in zoom mechanisms.
   */
  abstract fun setBuiltInZoomControls(pigeon_instance: com.tencent.smtt.sdk.WebSettings, enabled: Boolean)

  /** Enables or disables file access within WebView. */
  abstract fun setAllowFileAccess(pigeon_instance: com.tencent.smtt.sdk.WebSettings, enabled: Boolean)

  /** Enables or disables content URL access within WebView. */
  abstract fun setAllowContentAccess(pigeon_instance: com.tencent.smtt.sdk.WebSettings, enabled: Boolean)

  /** Sets whether Geolocation is enabled within WebView. */
  abstract fun setGeolocationEnabled(pigeon_instance: com.tencent.smtt.sdk.WebSettings, enabled: Boolean)

  /** Sets the text zoom of the page in percent. */
  abstract fun setTextZoom(pigeon_instance: com.tencent.smtt.sdk.WebSettings, textZoom: Long)

  /** Gets the WebView's user-agent string. */
  abstract fun getUserAgentString(pigeon_instance: com.tencent.smtt.sdk.WebSettings): String

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiWebSettings?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setDomStorageEnabled", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val flagArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setDomStorageEnabled(pigeon_instanceArg, flagArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setJavaScriptCanOpenWindowsAutomatically", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val flagArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setJavaScriptCanOpenWindowsAutomatically(pigeon_instanceArg, flagArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setSupportMultipleWindows", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val supportArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSupportMultipleWindows(pigeon_instanceArg, supportArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setJavaScriptEnabled", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val flagArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setJavaScriptEnabled(pigeon_instanceArg, flagArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setUserAgentString", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val userAgentStringArg = args[1] as String?
            val wrapped: List<Any?> = try {
              api.setUserAgentString(pigeon_instanceArg, userAgentStringArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setMediaPlaybackRequiresUserGesture", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val requireArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setMediaPlaybackRequiresUserGesture(pigeon_instanceArg, requireArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setSupportZoom", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val supportArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSupportZoom(pigeon_instanceArg, supportArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setLoadWithOverviewMode", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val overviewArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setLoadWithOverviewMode(pigeon_instanceArg, overviewArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setUseWideViewPort", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val useArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setUseWideViewPort(pigeon_instanceArg, useArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setDisplayZoomControls", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val enabledArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setDisplayZoomControls(pigeon_instanceArg, enabledArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setBuiltInZoomControls", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val enabledArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setBuiltInZoomControls(pigeon_instanceArg, enabledArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setAllowFileAccess", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val enabledArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setAllowFileAccess(pigeon_instanceArg, enabledArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setAllowContentAccess", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val enabledArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setAllowContentAccess(pigeon_instanceArg, enabledArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setGeolocationEnabled", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val enabledArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setGeolocationEnabled(pigeon_instanceArg, enabledArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.setTextZoom", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val textZoomArg = args[1] as Long
            val wrapped: List<Any?> = try {
              api.setTextZoom(pigeon_instanceArg, textZoomArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebSettings.getUserAgentString", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebSettings
            val wrapped: List<Any?> = try {
              listOf(api.getUserAgentString(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebSettings and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.WebSettings, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebSettings.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * A JavaScript interface for exposing Javascript callbacks to Dart.
 *
 * This is a custom class for the wrapper that is annotated with
 * [JavascriptInterface](https://developer.android.com/reference/android/webkit/JavascriptInterface).
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiJavaScriptChannel(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun pigeon_defaultConstructor(channelName: String): JavaScriptChannel

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiJavaScriptChannel?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.pigeon_defaultConstructor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val channelNameArg = args[1] as String
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.pigeon_defaultConstructor(channelNameArg), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of JavaScriptChannel and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: JavaScriptChannel, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      callback(
          Result.failure(
              AndroidWebKitError("new-instance-error", "Attempting to create a new Dart instance of JavaScriptChannel, but the class has a nonnull callback method.", "")))
    }
  }

  /** Handles callbacks messages from JavaScript. */
  fun postMessage(pigeon_instanceArg: JavaScriptChannel, messageArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.JavaScriptChannel.postMessage"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, messageArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

}
/**
 * Receives various notifications and requests from a `WebView`.
 *
 * See https://developer.android.com/reference/android/webkit/WebViewClient.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebViewClient(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun pigeon_defaultConstructor(): com.tencent.smtt.sdk.WebViewClient

  /**
   * Sets the required synchronous return value for the Java method,
   * `WebViewClient.shouldOverrideUrlLoading(...)`.
   *
   * The Java method, `WebViewClient.shouldOverrideUrlLoading(...)`, requires
   * a boolean to be returned and this method sets the returned value for all
   * calls to the Java method.
   *
   * Setting this to true causes the current [WebView] to abort loading any URL
   * received by [requestLoading] or [urlLoading], while setting this to false
   * causes the [WebView] to continue loading a URL as usual.
   *
   * Defaults to false.
   */
  abstract fun setSynchronousReturnValueForShouldOverrideUrlLoading(pigeon_instance: com.tencent.smtt.sdk.WebViewClient, value: Boolean)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiWebViewClient?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebViewClient.pigeon_defaultConstructor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.pigeon_defaultConstructor(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebViewClient.setSynchronousReturnValueForShouldOverrideUrlLoading", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebViewClient
            val valueArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSynchronousReturnValueForShouldOverrideUrlLoading(pigeon_instanceArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebViewClient and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

  /** Notify the host application that a page has started loading. */
  fun onPageStarted(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageStarted"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Notify the host application that a page has finished loading. */
  fun onPageFinished(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageFinished"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that an HTTP error has been received from the
   * server while loading a resource.
   */
  fun onReceivedHttpError(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, requestArg: com.tencent.smtt.export.external.interfaces.WebResourceRequest, responseArg: com.tencent.smtt.export.external.interfaces.WebResourceResponse, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpError"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, requestArg, responseArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Report web resource loading error to the host application. */
  @androidx.annotation.RequiresApi(api = 23)
  fun onReceivedRequestError(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, requestArg: com.tencent.smtt.export.external.interfaces.WebResourceRequest, errorArg: com.tencent.smtt.export.external.interfaces.WebResourceError, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestError"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, requestArg, errorArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Report web resource loading error to the host application. */
  fun onReceivedRequestErrorCompat(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, requestArg: com.tencent.smtt.export.external.interfaces.WebResourceRequest, errorArg: androidx.webkit.WebResourceErrorCompat, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedRequestErrorCompat"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, requestArg, errorArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Report an error to the host application. */
  fun onReceivedError(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, errorCodeArg: Long, descriptionArg: String, failingUrlArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedError"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, errorCodeArg, descriptionArg, failingUrlArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Give the host application a chance to take control when a URL is about to
   * be loaded in the current WebView.
   */
  fun requestLoading(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, requestArg: com.tencent.smtt.export.external.interfaces.WebResourceRequest, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.requestLoading"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, requestArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Give the host application a chance to take control when a URL is about to
   * be loaded in the current WebView.
   */
  fun urlLoading(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.urlLoading"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Notify the host application to update its visited links database. */
  fun doUpdateVisitedHistory(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, isReloadArg: Boolean, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.doUpdateVisitedHistory"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg, isReloadArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notifies the host application that the WebView received an HTTP
   * authentication request.
   */
  fun onReceivedHttpAuthRequest(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, webViewArg: com.tencent.smtt.sdk.WebView, handlerArg: com.tencent.smtt.export.external.interfaces.HttpAuthHandler, hostArg: String, realmArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedHttpAuthRequest"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, handlerArg, hostArg, realmArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Ask the host application if the browser should resend data as the
   * requested page was a result of a POST.
   */
  fun onFormResubmission(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, dontResendArg: android.os.Message, resendArg: android.os.Message, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onFormResubmission"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, dontResendArg, resendArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that the WebView will load the resource
   * specified by the given url.
   */
  fun onLoadResource(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, urlArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onLoadResource"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, urlArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that WebView content left over from previous
   * page navigations will no longer be drawn.
   */
  fun onPageCommitVisible(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, urlArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onPageCommitVisible"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, urlArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Notify the host application to handle a SSL client certificate request. */
  fun onReceivedClientCertRequest(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, requestArg: com.tencent.smtt.export.external.interfaces.ClientCertRequest, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedClientCertRequest"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, requestArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that a request to automatically log in the
   * user has been processed.
   */
  fun onReceivedLoginRequest(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, realmArg: String, accountArg: String?, argsArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedLoginRequest"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, realmArg, accountArg, argsArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notifies the host application that an SSL error occurred while loading a
   * resource.
   */
  fun onReceivedSslError(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, handlerArg: com.tencent.smtt.export.external.interfaces.SslErrorHandler, errorArg: com.tencent.smtt.export.external.interfaces.SslError, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onReceivedSslError"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, handlerArg, errorArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that the scale applied to the WebView has
   * changed.
   */
  fun onScaleChanged(pigeon_instanceArg: com.tencent.smtt.sdk.WebViewClient, viewArg: com.tencent.smtt.sdk.WebView, oldScaleArg: Double, newScaleArg: Double, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebViewClient.onScaleChanged"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, oldScaleArg, newScaleArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

}
/**
 * Handles notifications that a file should be downloaded.
 *
 * See https://developer.android.com/reference/android/webkit/DownloadListener.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiDownloadListener(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun pigeon_defaultConstructor(): com.tencent.smtt.sdk.DownloadListener

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiDownloadListener?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.DownloadListener.pigeon_defaultConstructor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.pigeon_defaultConstructor(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of DownloadListener and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.DownloadListener, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      callback(
          Result.failure(
              AndroidWebKitError("new-instance-error", "Attempting to create a new Dart instance of DownloadListener, but the class has a nonnull callback method.", "")))
    }
  }

  /** Notify the host application that a file should be downloaded. */
  fun onDownloadStart(pigeon_instanceArg: com.tencent.smtt.sdk.DownloadListener, urlArg: String, userAgentArg: String, contentDispositionArg: String, mimetypeArg: String, contentLengthArg: Long, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.DownloadListener.onDownloadStart"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, urlArg, userAgentArg, contentDispositionArg, mimetypeArg, contentLengthArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

}
/**
 * Handles notification of JavaScript dialogs, favicons, titles, and the
 * progress.
 *
 * See https://developer.android.com/reference/android/webkit/WebChromeClient.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebChromeClient(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun pigeon_defaultConstructor(): io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl

  /**
   * Sets the required synchronous return value for the Java method,
   * `WebChromeClient.onShowFileChooser(...)`.
   *
   * The Java method, `WebChromeClient.onShowFileChooser(...)`, requires
   * a boolean to be returned and this method sets the returned value for all
   * calls to the Java method.
   *
   * Setting this to true indicates that all file chooser requests should be
   * handled by `onShowFileChooser` and the returned list of Strings will be
   * returned to the WebView. Otherwise, the client will use the default
   * handling and the returned value in `onShowFileChooser` will be ignored.
   *
   * Requires `onShowFileChooser` to be nonnull.
   *
   * Defaults to false.
   */
  abstract fun setSynchronousReturnValueForOnShowFileChooser(pigeon_instance: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, value: Boolean)

  /**
   * Sets the required synchronous return value for the Java method,
   * `WebChromeClient.onConsoleMessage(...)`.
   *
   * The Java method, `WebChromeClient.onConsoleMessage(...)`, requires
   * a boolean to be returned and this method sets the returned value for all
   * calls to the Java method.
   *
   * Setting this to true indicates that the client is handling all console
   * messages.
   *
   * Requires `onConsoleMessage` to be nonnull.
   *
   * Defaults to false.
   */
  abstract fun setSynchronousReturnValueForOnConsoleMessage(pigeon_instance: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, value: Boolean)

  /**
   * Sets the required synchronous return value for the Java method,
   * `WebChromeClient.onJsAlert(...)`.
   *
   * The Java method, `WebChromeClient.onJsAlert(...)`, requires a boolean to
   * be returned and this method sets the returned value for all calls to the
   * Java method.
   *
   * Setting this to true indicates that the client is handling all console
   * messages.
   *
   * Requires `onJsAlert` to be nonnull.
   *
   * Defaults to false.
   */
  abstract fun setSynchronousReturnValueForOnJsAlert(pigeon_instance: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, value: Boolean)

  /**
   * Sets the required synchronous return value for the Java method,
   * `WebChromeClient.onJsConfirm(...)`.
   *
   * The Java method, `WebChromeClient.onJsConfirm(...)`, requires a boolean to
   * be returned and this method sets the returned value for all calls to the
   * Java method.
   *
   * Setting this to true indicates that the client is handling all console
   * messages.
   *
   * Requires `onJsConfirm` to be nonnull.
   *
   * Defaults to false.
   */
  abstract fun setSynchronousReturnValueForOnJsConfirm(pigeon_instance: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, value: Boolean)

  /**
   * Sets the required synchronous return value for the Java method,
   * `WebChromeClient.onJsPrompt(...)`.
   *
   * The Java method, `WebChromeClient.onJsPrompt(...)`, requires a boolean to
   * be returned and this method sets the returned value for all calls to the
   * Java method.
   *
   * Setting this to true indicates that the client is handling all console
   * messages.
   *
   * Requires `onJsPrompt` to be nonnull.
   *
   * Defaults to false.
   */
  abstract fun setSynchronousReturnValueForOnJsPrompt(pigeon_instance: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, value: Boolean)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiWebChromeClient?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.pigeon_defaultConstructor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.pigeon_defaultConstructor(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnShowFileChooser", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl
            val valueArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSynchronousReturnValueForOnShowFileChooser(pigeon_instanceArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnConsoleMessage", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl
            val valueArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSynchronousReturnValueForOnConsoleMessage(pigeon_instanceArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnJsAlert", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl
            val valueArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSynchronousReturnValueForOnJsAlert(pigeon_instanceArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnJsConfirm", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl
            val valueArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSynchronousReturnValueForOnJsConfirm(pigeon_instanceArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.setSynchronousReturnValueForOnJsPrompt", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl
            val valueArg = args[1] as Boolean
            val wrapped: List<Any?> = try {
              api.setSynchronousReturnValueForOnJsPrompt(pigeon_instanceArg, valueArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebChromeClient and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      callback(
          Result.failure(
              AndroidWebKitError("new-instance-error", "Attempting to create a new Dart instance of WebChromeClient, but the class has a nonnull callback method.", "")))
    }
  }

  /** Tell the host application the current progress of loading a page. */
  fun onProgressChanged(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, webViewArg: com.tencent.smtt.sdk.WebView, progressArg: Long, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onProgressChanged"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, progressArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Tell the client to show a file chooser. */
  fun onShowFileChooser(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, webViewArg: com.tencent.smtt.sdk.WebView, paramsArg: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams, callback: (Result<List<String>>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowFileChooser"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, paramsArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else if (it[0] == null) {
          callback(Result.failure(AndroidWebKitError("null-error", "Flutter api returned null value for non-null return value.", "")))
        } else {
          val output = it[0] as List<String>
          callback(Result.success(output))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that web content is requesting permission to
   * access the specified resources and the permission currently isn't granted
   * or denied.
   */
  fun onPermissionRequest(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, requestArg: com.tencent.smtt.export.external.interfaces.PermissionRequest, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onPermissionRequest"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, requestArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Callback to Dart function `WebChromeClient.onShowCustomView`. */
  fun onShowCustomView(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, viewArg: android.view.View, callbackArg: com.tencent.smtt.export.external.interfaces.IX5WebChromeClient.CustomViewCallback, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onShowCustomView"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, viewArg, callbackArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that the current page has entered full screen
   * mode.
   */
  fun onHideCustomView(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onHideCustomView"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that web content from the specified origin is
   * attempting to use the Geolocation API, but no permission state is
   * currently set for that origin.
   */
  fun onGeolocationPermissionsShowPrompt(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, originArg: String, callbackArg: com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsShowPrompt"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, originArg, callbackArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that a request for Geolocation permissions,
   * made with a previous call to `onGeolocationPermissionsShowPrompt` has been
   * canceled.
   */
  fun onGeolocationPermissionsHidePrompt(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onGeolocationPermissionsHidePrompt"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /** Report a JavaScript console message to the host application. */
  fun onConsoleMessage(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, messageArg: com.tencent.smtt.export.external.interfaces.ConsoleMessage, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onConsoleMessage"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, messageArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that the web page wants to display a
   * JavaScript `alert()` dialog.
   */
  fun onJsAlert(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, messageArg: String, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsAlert"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg, messageArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          callback(Result.success(Unit))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that the web page wants to display a
   * JavaScript `confirm()` dialog.
   */
  fun onJsConfirm(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, messageArg: String, callback: (Result<Boolean>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsConfirm"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg, messageArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else if (it[0] == null) {
          callback(Result.failure(AndroidWebKitError("null-error", "Flutter api returned null value for non-null return value.", "")))
        } else {
          val output = it[0] as Boolean
          callback(Result.success(output))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

  /**
   * Notify the host application that the web page wants to display a
   * JavaScript `prompt()` dialog.
   */
  fun onJsPrompt(pigeon_instanceArg: io.flutter.plugins.webviewflutter.WebChromeClientProxyApi.WebChromeClientImpl, webViewArg: com.tencent.smtt.sdk.WebView, urlArg: String, messageArg: String, defaultValueArg: String, callback: (Result<String?>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
      return
    }
    val binaryMessenger = pigeonRegistrar.binaryMessenger
    val codec = pigeonRegistrar.codec
    val channelName = "dev.flutter.pigeon.webview_flutter_android.WebChromeClient.onJsPrompt"
    val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
    channel.send(listOf(pigeon_instanceArg, webViewArg, urlArg, messageArg, defaultValueArg)) {
      if (it is List<*>) {
        if (it.size > 1) {
          callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
        } else {
          val output = it[0] as String?
          callback(Result.success(output))
        }
      } else {
        callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
      } 
    }
  }

}
/**
 * Provides access to the assets registered as part of the App bundle.
 *
 * Convenience class for accessing Flutter asset resources.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiFlutterAssetManager(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** The global instance of the `FlutterAssetManager`. */
  abstract fun instance(): io.flutter.plugins.webviewflutter.FlutterAssetManager

  /**
   * Returns a String array of all the assets at the given path.
   *
   * Throws an IOException in case I/O operations were interrupted.
   */
  abstract fun list(pigeon_instance: io.flutter.plugins.webviewflutter.FlutterAssetManager, path: String): List<String>

  /**
   * Gets the relative file path to the Flutter asset with the given name, including the file's
   * extension, e.g., "myImage.jpg".
   *
   * The returned file path is relative to the Android app's standard asset's
   * directory. Therefore, the returned path is appropriate to pass to
   * Android's AssetManager, but the path is not appropriate to load as an
   * absolute path.
   */
  abstract fun getAssetFilePathByName(pigeon_instance: io.flutter.plugins.webviewflutter.FlutterAssetManager, name: String): String

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiFlutterAssetManager?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.instance", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.instance(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.list", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.FlutterAssetManager
            val pathArg = args[1] as String
            val wrapped: List<Any?> = try {
              listOf(api.list(pigeon_instanceArg, pathArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.getAssetFilePathByName", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as io.flutter.plugins.webviewflutter.FlutterAssetManager
            val nameArg = args[1] as String
            val wrapped: List<Any?> = try {
              listOf(api.getAssetFilePathByName(pigeon_instanceArg, nameArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of FlutterAssetManager and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: io.flutter.plugins.webviewflutter.FlutterAssetManager, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.FlutterAssetManager.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * This class is used to manage the JavaScript storage APIs provided by the
 * WebView.
 *
 * See https://developer.android.com/reference/android/webkit/WebStorage.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiWebStorage(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun instance(): com.tencent.smtt.sdk.WebStorage

  /** Clears all storage currently being used by the JavaScript storage APIs. */
  abstract fun deleteAllData(pigeon_instance: com.tencent.smtt.sdk.WebStorage)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiWebStorage?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebStorage.instance", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_identifierArg = args[0] as Long
            val wrapped: List<Any?> = try {
              api.pigeonRegistrar.instanceManager.addDartCreatedInstance(api.instance(), pigeon_identifierArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.WebStorage.deleteAllData", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.sdk.WebStorage
            val wrapped: List<Any?> = try {
              api.deleteAllData(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of WebStorage and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.WebStorage, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.WebStorage.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Parameters used in the `WebChromeClient.onShowFileChooser` method.
 *
 * See https://developer.android.com/reference/android/webkit/WebChromeClient.FileChooserParams.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiFileChooserParams(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Preference for a live media captured value (e.g. Camera, Microphone). */
  abstract fun isCaptureEnabled(pigeon_instance: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams): Boolean

  /** An array of acceptable MIME types. */
  abstract fun acceptTypes(pigeon_instance: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams): List<String>

  /** File chooser mode. */
  abstract fun mode(pigeon_instance: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams): FileChooserMode

  /** File name of a default selection if specified, or null. */
  abstract fun filenameHint(pigeon_instance: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams): String?

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of FileChooserParams and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val isCaptureEnabledArg = isCaptureEnabled(pigeon_instanceArg)
      val acceptTypesArg = acceptTypes(pigeon_instanceArg)
      val modeArg = mode(pigeon_instanceArg)
      val filenameHintArg = filenameHint(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.FileChooserParams.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, isCaptureEnabledArg, acceptTypesArg, modeArg, filenameHintArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * This class defines a permission request and is used when web content
 * requests access to protected resources.
 *
 * See https://developer.android.com/reference/android/webkit/PermissionRequest.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiPermissionRequest(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  abstract fun resources(pigeon_instance: com.tencent.smtt.export.external.interfaces.PermissionRequest): List<String>

  /**
   * Call this method to grant origin the permission to access the given
   * resources.
   */
  abstract fun grant(pigeon_instance: com.tencent.smtt.export.external.interfaces.PermissionRequest, resources: List<String>)

  /** Call this method to deny the request. */
  abstract fun deny(pigeon_instance: com.tencent.smtt.export.external.interfaces.PermissionRequest)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiPermissionRequest?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.PermissionRequest.grant", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.PermissionRequest
            val resourcesArg = args[1] as List<String>
            val wrapped: List<Any?> = try {
              api.grant(pigeon_instanceArg, resourcesArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.PermissionRequest.deny", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.PermissionRequest
            val wrapped: List<Any?> = try {
              api.deny(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of PermissionRequest and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.PermissionRequest, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val resourcesArg = resources(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.PermissionRequest.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, resourcesArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * A callback interface used by the host application to notify the current page
 * that its custom view has been dismissed.
 *
 * See https://developer.android.com/reference/android/webkit/WebChromeClient.CustomViewCallback.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiCustomViewCallback(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Invoked when the host application dismisses the custom view. */
  abstract fun onCustomViewHidden(pigeon_instance: com.tencent.smtt.export.external.interfaces.IX5WebChromeClient.CustomViewCallback)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiCustomViewCallback?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.CustomViewCallback.onCustomViewHidden", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.IX5WebChromeClient.CustomViewCallback
            val wrapped: List<Any?> = try {
              api.onCustomViewHidden(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of CustomViewCallback and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.IX5WebChromeClient.CustomViewCallback, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.CustomViewCallback.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * This class represents the basic building block for user interface
 * components.
 *
 * See https://developer.android.com/reference/android/view/View.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiView(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Set the scrolled position of your view. */
  abstract fun scrollTo(pigeon_instance: android.view.View, x: Long, y: Long)

  /** Move the scrolled position of your view. */
  abstract fun scrollBy(pigeon_instance: android.view.View, x: Long, y: Long)

  /** Return the scrolled position of this view. */
  abstract fun getScrollPosition(pigeon_instance: android.view.View): WebViewPoint

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiView?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.View.scrollTo", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.view.View
            val xArg = args[1] as Long
            val yArg = args[2] as Long
            val wrapped: List<Any?> = try {
              api.scrollTo(pigeon_instanceArg, xArg, yArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.View.scrollBy", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.view.View
            val xArg = args[1] as Long
            val yArg = args[2] as Long
            val wrapped: List<Any?> = try {
              api.scrollBy(pigeon_instanceArg, xArg, yArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.View.getScrollPosition", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.view.View
            val wrapped: List<Any?> = try {
              listOf(api.getScrollPosition(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of View and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: android.view.View, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.View.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * A callback interface used by the host application to set the Geolocation
 * permission state for an origin.
 *
 * See https://developer.android.com/reference/android/webkit/GeolocationPermissions.Callback.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiGeolocationPermissionsCallback(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Sets the Geolocation permission state for the supplied origin. */
  abstract fun invoke(pigeon_instance: com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback, origin: String, allow: Boolean, retain: Boolean)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiGeolocationPermissionsCallback?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallback.invoke", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback
            val originArg = args[1] as String
            val allowArg = args[2] as Boolean
            val retainArg = args[3] as Boolean
            val wrapped: List<Any?> = try {
              api.invoke(pigeon_instanceArg, originArg, allowArg, retainArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of GeolocationPermissionsCallback and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallback.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Represents a request for HTTP authentication.
 *
 * See https://developer.android.com/reference/android/webkit/HttpAuthHandler.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiHttpAuthHandler(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /**
   * Gets whether the credentials stored for the current host (i.e. the host
   * for which `WebViewClient.onReceivedHttpAuthRequest` was called) are
   * suitable for use.
   */
  abstract fun useHttpAuthUsernamePassword(pigeon_instance: com.tencent.smtt.export.external.interfaces.HttpAuthHandler): Boolean

  /** Instructs the WebView to cancel the authentication request.. */
  abstract fun cancel(pigeon_instance: com.tencent.smtt.export.external.interfaces.HttpAuthHandler)

  /**
   * Instructs the WebView to proceed with the authentication with the given
   * credentials.
   */
  abstract fun proceed(pigeon_instance: com.tencent.smtt.export.external.interfaces.HttpAuthHandler, username: String, password: String)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiHttpAuthHandler?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.useHttpAuthUsernamePassword", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.HttpAuthHandler
            val wrapped: List<Any?> = try {
              listOf(api.useHttpAuthUsernamePassword(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.cancel", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.HttpAuthHandler
            val wrapped: List<Any?> = try {
              api.cancel(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.proceed", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.HttpAuthHandler
            val usernameArg = args[1] as String
            val passwordArg = args[2] as String
            val wrapped: List<Any?> = try {
              api.proceed(pigeon_instanceArg, usernameArg, passwordArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of HttpAuthHandler and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.HttpAuthHandler, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.HttpAuthHandler.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Defines a message containing a description and arbitrary data object that
 * can be sent to a `Handler`.
 *
 * See https://developer.android.com/reference/android/os/Message.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiAndroidMessage(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /**
   * Sends this message to the Android native `Handler` specified by
   * getTarget().
   *
   * Throws a null pointer exception if this field has not been set.
   */
  abstract fun sendToTarget(pigeon_instance: android.os.Message)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiAndroidMessage?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.AndroidMessage.sendToTarget", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.os.Message
            val wrapped: List<Any?> = try {
              api.sendToTarget(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of AndroidMessage and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: android.os.Message, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.AndroidMessage.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Defines a message containing a description and arbitrary data object that
 * can be sent to a `Handler`.
 *
 * See https://developer.android.com/reference/android/webkit/ClientCertRequest.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiClientCertRequest(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Cancel this request. */
  abstract fun cancel(pigeon_instance: com.tencent.smtt.export.external.interfaces.ClientCertRequest)

  /** Ignore the request for now. */
  abstract fun ignore(pigeon_instance: com.tencent.smtt.export.external.interfaces.ClientCertRequest)

  /** Proceed with the specified private key and client certificate chain. */
  abstract fun proceed(pigeon_instance: com.tencent.smtt.export.external.interfaces.ClientCertRequest, privateKey: java.security.PrivateKey, chain: List<java.security.cert.X509Certificate>)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiClientCertRequest?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.cancel", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.ClientCertRequest
            val wrapped: List<Any?> = try {
              api.cancel(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.ignore", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.ClientCertRequest
            val wrapped: List<Any?> = try {
              api.ignore(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.proceed", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.ClientCertRequest
            val privateKeyArg = args[1] as java.security.PrivateKey
            val chainArg = args[2] as List<java.security.cert.X509Certificate>
            val wrapped: List<Any?> = try {
              api.proceed(pigeon_instanceArg, privateKeyArg, chainArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of ClientCertRequest and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.ClientCertRequest, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.ClientCertRequest.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * A private key.
 *
 * The purpose of this interface is to group (and provide type safety for) all
 * private key interfaces.
 *
 * See https://developer.android.com/reference/java/security/PrivateKey.
 */
@Suppress("UNCHECKED_CAST")
open class PigeonApiPrivateKey(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of PrivateKey and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: java.security.PrivateKey, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.PrivateKey.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Abstract class for X.509 certificates.
 *
 * This provides a standard way to access all the attributes of an X.509
 * certificate.
 *
 * See https://developer.android.com/reference/java/security/cert/X509Certificate.
 */
@Suppress("UNCHECKED_CAST")
open class PigeonApiX509Certificate(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of X509Certificate and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: java.security.cert.X509Certificate, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.X509Certificate.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * Represents a request for handling an SSL error.
 *
 * See https://developer.android.com/reference/android/webkit/SslErrorHandler.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiSslErrorHandler(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /**
   * Instructs the WebView that encountered the SSL certificate error to
   * terminate communication with the server.
   */
  abstract fun cancel(pigeon_instance: com.tencent.smtt.export.external.interfaces.SslErrorHandler)

  /**
   * Instructs the WebView that encountered the SSL certificate error to ignore
   * the error and continue communicating with the server.
   */
  abstract fun proceed(pigeon_instance: com.tencent.smtt.export.external.interfaces.SslErrorHandler)

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiSslErrorHandler?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.cancel", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.SslErrorHandler
            val wrapped: List<Any?> = try {
              api.cancel(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.proceed", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.SslErrorHandler
            val wrapped: List<Any?> = try {
              api.proceed(pigeon_instanceArg)
              listOf(null)
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of SslErrorHandler and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.SslErrorHandler, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.SslErrorHandler.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * This class represents a set of one or more SSL errors and the associated SSL
 * certificate.
 *
 * See https://developer.android.com/reference/android/net/http/SslError.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiSslError(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Gets the SSL certificate associated with this object. */
  abstract fun certificate(pigeon_instance: com.tencent.smtt.export.external.interfaces.SslError): android.net.http.SslCertificate

  /** Gets the URL associated with this object. */
  abstract fun url(pigeon_instance: com.tencent.smtt.export.external.interfaces.SslError): String

  /** Gets the most severe SSL error in this object's set of errors. */
  abstract fun getPrimaryError(pigeon_instance: com.tencent.smtt.export.external.interfaces.SslError): SslErrorType

  /** Determines whether this object includes the supplied error. */
  abstract fun hasError(pigeon_instance: com.tencent.smtt.export.external.interfaces.SslError, error: SslErrorType): Boolean

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiSslError?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslError.getPrimaryError", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.SslError
            val wrapped: List<Any?> = try {
              listOf(api.getPrimaryError(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslError.hasError", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as com.tencent.smtt.export.external.interfaces.SslError
            val errorArg = args[1] as SslErrorType
            val wrapped: List<Any?> = try {
              listOf(api.hasError(pigeon_instanceArg, errorArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of SslError and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: com.tencent.smtt.export.external.interfaces.SslError, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val certificateArg = certificate(pigeon_instanceArg)
      val urlArg = url(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.SslError.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg, certificateArg, urlArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * A distinguished name helper class.
 *
 * A 3-tuple of:
 * the most specific common name (CN)
 * the most specific organization (O)
 * the most specific organizational unit (OU)
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiSslCertificateDName(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** The most specific Common-name (CN) component of this name. */
  abstract fun getCName(pigeon_instance: android.net.http.SslCertificate.DName): String

  /** The distinguished name (normally includes CN, O, and OU names). */
  abstract fun getDName(pigeon_instance: android.net.http.SslCertificate.DName): String

  /** The most specific Organization (O) component of this name. */
  abstract fun getOName(pigeon_instance: android.net.http.SslCertificate.DName): String

  /** The most specific Organizational Unit (OU) component of this name. */
  abstract fun getUName(pigeon_instance: android.net.http.SslCertificate.DName): String

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiSslCertificateDName?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getCName", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate.DName
            val wrapped: List<Any?> = try {
              listOf(api.getCName(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getDName", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate.DName
            val wrapped: List<Any?> = try {
              listOf(api.getDName(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getOName", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate.DName
            val wrapped: List<Any?> = try {
              listOf(api.getOName(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.getUName", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate.DName
            val wrapped: List<Any?> = try {
              listOf(api.getUName(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of SslCertificateDName and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: android.net.http.SslCertificate.DName, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.SslCertificateDName.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
/**
 * SSL certificate info (certificate details) class.
 *
 * See https://developer.android.com/reference/android/net/http/SslCertificate.
 */
@Suppress("UNCHECKED_CAST")
abstract class PigeonApiSslCertificate(open val pigeonRegistrar: AndroidWebkitLibraryPigeonProxyApiRegistrar) {
  /** Issued-by distinguished name or null if none has been set. */
  abstract fun getIssuedBy(pigeon_instance: android.net.http.SslCertificate): android.net.http.SslCertificate.DName?

  /** Issued-to distinguished name or null if none has been set. */
  abstract fun getIssuedTo(pigeon_instance: android.net.http.SslCertificate): android.net.http.SslCertificate.DName?

  /**
   * Not-after date from the certificate validity period or null if none has been
   * set.
   */
  abstract fun getValidNotAfterMsSinceEpoch(pigeon_instance: android.net.http.SslCertificate): Long?

  /**
   * Not-before date from the certificate validity period or null if none has
   * been set.
   */
  abstract fun getValidNotBeforeMsSinceEpoch(pigeon_instance: android.net.http.SslCertificate): Long?

  /**
   * The X509Certificate used to create this SslCertificate or null if no
   * certificate was provided.
   *
   * Always returns null on Android versions below Q.
   */
  abstract fun getX509Certificate(pigeon_instance: android.net.http.SslCertificate): java.security.cert.X509Certificate?

  companion object {
    @Suppress("LocalVariableName")
    fun setUpMessageHandlers(binaryMessenger: BinaryMessenger, api: PigeonApiSslCertificate?) {
      val codec = api?.pigeonRegistrar?.codec ?: AndroidWebkitLibraryPigeonCodec()
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificate.getIssuedBy", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate
            val wrapped: List<Any?> = try {
              listOf(api.getIssuedBy(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificate.getIssuedTo", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate
            val wrapped: List<Any?> = try {
              listOf(api.getIssuedTo(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificate.getValidNotAfterMsSinceEpoch", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate
            val wrapped: List<Any?> = try {
              listOf(api.getValidNotAfterMsSinceEpoch(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificate.getValidNotBeforeMsSinceEpoch", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate
            val wrapped: List<Any?> = try {
              listOf(api.getValidNotBeforeMsSinceEpoch(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.webview_flutter_android.SslCertificate.getX509Certificate", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val pigeon_instanceArg = args[0] as android.net.http.SslCertificate
            val wrapped: List<Any?> = try {
              listOf(api.getX509Certificate(pigeon_instanceArg))
            } catch (exception: Throwable) {
              AndroidWebkitLibraryPigeonUtils.wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }

  @Suppress("LocalVariableName", "FunctionName")
  /** Creates a Dart instance of SslCertificate and attaches it to [pigeon_instanceArg]. */
  fun pigeon_newInstance(pigeon_instanceArg: android.net.http.SslCertificate, callback: (Result<Unit>) -> Unit)
{
    if (pigeonRegistrar.ignoreCallsToDart) {
      callback(
          Result.failure(
              AndroidWebKitError("ignore-calls-error", "Calls to Dart are being ignored.", "")))
    }     else if (pigeonRegistrar.instanceManager.containsInstance(pigeon_instanceArg)) {
      callback(Result.success(Unit))
    }     else {
      val pigeon_identifierArg = pigeonRegistrar.instanceManager.addHostCreatedInstance(pigeon_instanceArg)
      val binaryMessenger = pigeonRegistrar.binaryMessenger
      val codec = pigeonRegistrar.codec
      val channelName = "dev.flutter.pigeon.webview_flutter_android.SslCertificate.pigeon_newInstance"
      val channel = BasicMessageChannel<Any?>(binaryMessenger, channelName, codec)
      channel.send(listOf(pigeon_identifierArg)) {
        if (it is List<*>) {
          if (it.size > 1) {
            callback(Result.failure(AndroidWebKitError(it[0] as String, it[1] as String, it[2] as String?)))
          } else {
            callback(Result.success(Unit))
          }
        } else {
          callback(Result.failure(AndroidWebkitLibraryPigeonUtils.createConnectionError(channelName)))
        } 
      }
    }
  }

}
