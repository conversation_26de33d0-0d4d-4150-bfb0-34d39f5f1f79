// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.webviewflutter;

import androidx.annotation.NonNull;

import com.tencent.smtt.export.external.interfaces.SslError;

/**
 * ProxyApi implementation for {@link SslError}. This class may handle instantiating native object
 * instances that are attached to a Dart instance or handle method calls on the associated native
 * class or an instance of that class.
 */
class SslErrorProxyApi extends PigeonApiSslError {


    // Copy from android.net.http.SslError
    /**
     * The certificate is not yet valid
     */
    public static final int SSL_NOTYETVALID = 0;
    /**
     * The certificate has expired
     */
    public static final int SSL_EXPIRED = 1;
    /**
     * Hostname mismatch
     */
    public static final int SSL_IDMISMATCH = 2;
    /**
     * The certificate authority is not trusted
     */
    public static final int SSL_UNTRUSTED = 3;
    /**
     * The date of the certificate is invalid
     */
    public static final int SSL_DATE_INVALID = 4;
    /**
     * A generic error occurred
     */
    public static final int SSL_INVALID = 5;

    SslErrorProxyApi(@NonNull ProxyApiRegistrar pigeonRegistrar) {
        super(pigeonRegistrar);
    }

    @NonNull
    @Override
    public ProxyApiRegistrar getPigeonRegistrar() {
        return (ProxyApiRegistrar) super.getPigeonRegistrar();
    }

    @NonNull
    @Override
    public android.net.http.SslCertificate certificate(@NonNull SslError pigeon_instance) {
        return pigeon_instance.getCertificate();
    }

    @NonNull
    @Override
    public String url(@NonNull SslError pigeon_instance) {
        return pigeon_instance.getUrl();
    }

    @NonNull
    @Override
    public SslErrorType getPrimaryError(@NonNull SslError pigeon_instance) {
        switch (pigeon_instance.getPrimaryError()) {
            case SSL_DATE_INVALID:
                return SslErrorType.DATE_INVALID;
            case SSL_EXPIRED:
                return SslErrorType.EXPIRED;
            case SSL_IDMISMATCH:
                return SslErrorType.ID_MISMATCH;
            case SSL_INVALID:
                return SslErrorType.INVALID;
            case SSL_NOTYETVALID:
                return SslErrorType.NOT_YET_VALID;
            case SSL_UNTRUSTED:
                return SslErrorType.UNTRUSTED;
            default:
                return SslErrorType.UNKNOWN;
        }
    }

    @Override
    public boolean hasError(@NonNull SslError pigeon_instance, @NonNull SslErrorType error) {
        int nativeError = -1;
        switch (error) {
            case DATE_INVALID:
                nativeError = SSL_DATE_INVALID;
                break;
            case EXPIRED:
                nativeError = SSL_EXPIRED;
                break;
            case ID_MISMATCH:
                nativeError = SSL_IDMISMATCH;
                break;
            case INVALID:
                nativeError = SSL_INVALID;
                break;
            case NOT_YET_VALID:
                nativeError = SSL_NOTYETVALID;
                break;
            case UNTRUSTED:
                nativeError = SSL_UNTRUSTED;
                break;
            case UNKNOWN:
                throw getPigeonRegistrar().createUnknownEnumException(error);
        }
        return pigeon_instance.hasError(nativeError);
    }
}
