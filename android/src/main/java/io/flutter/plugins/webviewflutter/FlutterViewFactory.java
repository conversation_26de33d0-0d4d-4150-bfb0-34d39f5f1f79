// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.webviewflutter;

import android.content.Context;
import android.util.Log;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.StandardMessageCodec;
import io.flutter.plugin.platform.PlatformView;
import io.flutter.plugin.platform.PlatformViewFactory;

class FlutterViewFactory extends PlatformViewFactory {
  private final AndroidWebkitLibraryPigeonInstanceManager instanceManager;

  FlutterViewFactory(AndroidWebkitLibraryPigeonInstanceManager instanceManager) {
    super(StandardMessageCodec.INSTANCE);
    this.instanceManager = instanceManager;
    Log.e("LSL", "FlutterViewFactory: 构造函数被调用, instanceManager=" + instanceManager);
  }

  @NonNull
  @Override
  public PlatformView create(Context context, int viewId, @Nullable Object args) {
    Log.e("LSL", "create: 尝试创建Webview, viewId=" + viewId + ", args=" + args);
    final Integer identifier = (Integer) args;
    if (identifier == null) {
      Log.e("LSL", "create: identifier is null!");
      throw new IllegalStateException("An identifier is required to retrieve a View instance.");
    }

    Log.e("LSL", "create: 尝试从instanceManager获取实例, identifier=" + identifier);
    final Object instance = instanceManager.getInstance(identifier);
    Log.e("LSL", "create: 获取到的实例=" + instance + ", 类型=" + (instance != null ? instance.getClass().getName() : "null"));

    if (instance instanceof PlatformView) {
      Log.e("LSL", "create: 实例是PlatformView类型，直接返回");
      return (PlatformView) instance;
    } else if (instance instanceof View) {
      Log.e("LSL", "create: 实例是View类型，包装为PlatformView返回");
      return new PlatformView() {
        @Override
        public View getView() {
          return (View) instance;
        }

        @Override
        public void dispose() {}
      };
    }

    Log.e("LSL", "create: 无法找到合适的实例，抛出异常");
    throw new IllegalStateException(
        "Unable to find a PlatformView or View instance: " + args + ", " + instance);
  }
}
